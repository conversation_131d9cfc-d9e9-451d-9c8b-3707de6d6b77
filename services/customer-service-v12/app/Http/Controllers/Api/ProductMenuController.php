<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DeliveryLocation;
use App\Models\KitchenMaster;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Setting;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

/**
 * Product Menu Controller
 *
 * Handles product menu retrieval based on kitchen's active menu type and product categories.
 * Logic: Get kitchen's menu type -> Find active product categories for that type -> Get active products
 */
class ProductMenuController extends Controller
{
    /**
     * Get product menu for a given city and/or delivery location
     *
     * Logic:
     * 1. Get kitchen and its menu type (using existing menu management logic)
     * 2. Find active product categories of type "meal"
     * 3. Get active products that match both the kitchen's menu type and product categories
     * 4. Bifurcate products by category name
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getProductMenu(Request $request): JsonResponse
    {
        try {
            // Validate input parameters
            $validator = Validator::make($request->all(), [
                'company_id' => 'required|integer|max:99999',
                'city' => 'nullable|string|max:100',
                'delivery_location' => 'nullable|string|max:255',
                'delivery_location_id' => 'nullable|integer|exists:delivery_locations,pk_location_code',
                'kitchen_id' => 'nullable|integer',
                // Optional date filters
                'date' => 'nullable|date_format:Y-m-d',
                'from_date' => 'nullable|date_format:Y-m-d',
                'to_date' => 'nullable|date_format:Y-m-d',
            ]);
            

            if ($validator->fails()) {
                return $this->validationErrorResponse($validator->errors());
            }

            $companyId = (int) $request->input('company_id');
            $city = $request->input('city');
            $deliveryLocation = $request->input('delivery_location');
            $deliveryLocationId = $request->input('delivery_location_id');
            $date = $request->input('date');
            $fromDate = $request->input('from_date');
            $toDate = $request->input('to_date');

            // If range provided, ensure both ends present and valid order
            if (($fromDate && !$toDate) || ($toDate && !$fromDate)) {
                return $this->validationErrorResponse(['date_range' => ['Both from_date and to_date are required when specifying a range.']]);
            }
            if ($fromDate && $toDate && $fromDate > $toDate) {
                return $this->validationErrorResponse(['date_range' => ['from_date must be less than or equal to to_date']]);
            }

            // Step 1: Get kitchen and menu type
            $kitchenId = $request->filled('kitchen_id') ? (int)$request->input('kitchen_id') : null;
            $kitchen = null;
            $deliveryLocationData = null;
            $menuType = null;
            if ($kitchenId !== null) {
                // Explicit kitchen provided
                $kitchen = KitchenMaster::where('pk_kitchen_code', $kitchenId)->first();
                if (!$kitchen) {
                    // If invalid id, fallback to default 1
                    $kitchen = KitchenMaster::where('pk_kitchen_code', 1)->first();
                }
                if (!$kitchen) {
                    return $this->validationErrorResponse(['kitchen_id' => ['Kitchen not found']]);
                }

                // Determine menu type using settings similar to getKitchenMenuType
                $kitchenMenuTypeKey = strtoupper($kitchen->kitchen_alias) . '_MENU_TYPE';
                $kitchenMenuTypeSetting = Setting::byKey($kitchenMenuTypeKey)->first();
                $globalMenuTypeSetting = Setting::byKey('MENU_TYPE')->first();
                if ($kitchenMenuTypeSetting) {
                    $menuType = $kitchenMenuTypeSetting->value;
                } elseif ($globalMenuTypeSetting) {
                    $menuType = $globalMenuTypeSetting->value;
                }
                if (!$menuType) {
                    return $this->buildErrorResponse(
                        'No menu type configured for this kitchen or globally',
                        404,
                        [
                            'kitchen_alias' => $kitchen->kitchen_alias,
                        ]
                    );
                }
            } else {
                // Use delivery location based resolution. If nothing provided, fallback to kitchen_id=1
                $hasLocationParams = $city || $deliveryLocation || $deliveryLocationId;
                if ($hasLocationParams) {
                    $menuData = $this->getKitchenMenuType($city, $deliveryLocation, $deliveryLocationId, $companyId);
                    if (!$menuData['success']) {
                        return response()->json($menuData, $menuData['status_code']);
                    }
                    $kitchen = $menuData['kitchen'];
                    $menuType = $menuData['menu_type'];
                    $deliveryLocationData = $menuData['delivery_location'];
                } else {
                    $kitchen = KitchenMaster::where('pk_kitchen_code', 1)->first();
                    if (!$kitchen) {
                        return $this->validationErrorResponse(['kitchen_id' => ['Default kitchen not found']]);
                    }
                    $kitchenMenuTypeKey = strtoupper($kitchen->kitchen_alias) . '_MENU_TYPE';
                    $kitchenMenuTypeSetting = Setting::byKey($kitchenMenuTypeKey)->first();
                    $globalMenuTypeSetting = Setting::byKey('MENU_TYPE')->first();
                    if ($kitchenMenuTypeSetting) {
                        $menuType = $kitchenMenuTypeSetting->value;
                    } elseif ($globalMenuTypeSetting) {
                        $menuType = $globalMenuTypeSetting->value;
                    }
                    if (!$menuType) {
                        return $this->buildErrorResponse('No menu type configured for default kitchen or globally', 404);
                    }
                }
            }

            $kitchenCode = $kitchen->pk_kitchen_code;

            // Step 2: Get active product categories of type "meal"
            $activeCategories = ProductCategory::active()
                ->mealType()
                ->orderBy('sequence')
                ->get();

            // Step 3 & 4: Get products and bifurcate by meal category types
            $productsByMealType = $this->getProductsByCategory($activeCategories, $menuType, $kitchenCode, $companyId, $date, $fromDate, $toDate);

            // If delivery location was not resolved (kitchen_id path), try to populate from delivery_location_id for response completeness
            if (!$deliveryLocationData && $deliveryLocationId) {
                $deliveryLocationData = DeliveryLocation::byCompany($companyId)
                    ->where('pk_location_code', $deliveryLocationId)
                    ->where('status', 1)
                    ->first();
            }

            return response()->json([
                'success' => true,
                'message' => 'Product menu retrieved successfully',
                'data' => [
                    'delivery_location' => $deliveryLocationData ? [
                        'id' => $deliveryLocationData->pk_location_code,
                        'name' => $deliveryLocationData->location,
                        'city' => $deliveryLocationData->city,
                        'fk_kitchen_code' => $deliveryLocationData->fk_kitchen_code,
                    ] : null,
                    'kitchen' => [
                        'code' => $kitchen->pk_kitchen_code,
                        'name' => $kitchen->kitchen_name,
                        'alias' => $kitchen->kitchen_alias,
                    ],
                    'menu_type' => $menuType,
                    'meal_types' => $productsByMealType,
                    'summary' => [
                        'total_meal_types' => count($productsByMealType),
                        'total_products' => array_sum(array_column($productsByMealType, 'product_count')),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return $this->exceptionResponse($e, 'Error retrieving product menu', $request->all());
        }
    }

    /**
     * Get kitchen and menu type using existing menu management logic
     *
     * @param string|null $city
     * @param string|null $deliveryLocation
     * @param int|null $deliveryLocationId
     * @return array
     */
    private function getKitchenMenuType(?string $city, ?string $deliveryLocation, ?int $deliveryLocationId, int $companyId): array
    {
        // Step 1: Determine the active delivery location
        $activeDeliveryLocation = $this->getActiveDeliveryLocation($city, $deliveryLocation, $deliveryLocationId, $companyId);

        if (!$activeDeliveryLocation) {
            return $this->buildErrorResponse(
                'No active delivery location found for the given criteria',
                404,
                [
                    'city' => $city,
                    'delivery_location' => $deliveryLocation,
                    'delivery_location_id' => $deliveryLocationId,
                ]
            );
        }

        // Step 2 & 3: Get kitchen data
        $kitchenData = $this->getKitchenData($activeDeliveryLocation);
        if (!$kitchenData['success']) {
            return $kitchenData;
        }

        $kitchen = $kitchenData['kitchen'];

        // Step 4: Look up menu type in settings (with fallback logic)
        $kitchenMenuTypeKey = strtoupper($kitchen->kitchen_alias) . '_MENU_TYPE';
        $kitchenMenuTypeSetting = Setting::byKey($kitchenMenuTypeKey)->first();

        // Always check for global MENU_TYPE setting
        $globalMenuTypeSetting = Setting::byKey('MENU_TYPE')->first();

        $menuType = null;

        if ($kitchenMenuTypeSetting) {
            // Kitchen-specific setting takes priority
            $menuType = $kitchenMenuTypeSetting->value;
        } elseif ($globalMenuTypeSetting) {
            // Fall back to global MENU_TYPE
            $menuType = $globalMenuTypeSetting->value;
        }

        if (!$menuType) {
            return $this->buildErrorResponse(
                'No menu type configured for this kitchen or globally',
                404,
                [
                    'kitchen_alias' => $kitchen->kitchen_alias,
                    'kitchen_setting_key' => $kitchenMenuTypeKey,
                ]
            );
        }

        return [
            'success' => true,
            'delivery_location' => $activeDeliveryLocation,
            'kitchen' => $kitchen,
            'menu_type' => $menuType,
        ];
    }

    /**
     * Get products bifurcated by meal category types (breakfast, lunch, dinner)
     *
     * @param \Illuminate\Database\Eloquent\Collection $categories
     * @param string $menuType
     * @return array
     */
    private function getProductsByCategory($categories, string $menuType, int $kitchenCode, int $companyId, ?string $date = null, ?string $fromDate = null, ?string $toDate = null): array
    {
        $result = [];
        $menuTypes = array_map('trim', explode(',', $menuType));
        
        // Get all active products from active meal-type categories
        $allProducts = collect();
        foreach ($categories as $category) {
            $products = Product::active()
                ->withProductCategory($category->product_category_name)
                ->withCategory($menuTypes)
                ->orderBy('pk_product_code', 'asc')
                ->get();
            $allProducts = $allProducts->merge($products);
        }

        // Bifurcate products by meal category types (breakfast, lunch, dinner)
        foreach ($menuTypes as $mealType) {
            $mealTypeProducts = $allProducts->filter(function ($product) use ($mealType) {
                // Check if product's category contains this meal type
                $productCategories = array_map('trim', explode(',', $product->category));
                return in_array($mealType, $productCategories);
            })->sortBy(function ($product) {
                // Sort products: Recommended items first, then by pk_product_code
                $isRecommended = stripos($product->name, 'Recommended') !== false ? 0 : 1;
                return [$isRecommended, $product->pk_product_code];
            })->map(function ($product) use ($mealType, $kitchenCode, $date, $fromDate, $toDate) {
                return [
                    'id' => $product->pk_product_code,
                    'name' => $product->name,
                    'description' => $product->description,
                    'unit_price' => $product->unit_price,
                    'category' => $product->category,
                    'product_type' => $product->product_type,
                    'food_type' => $product->food_type,
                    'image_path' => $this->assetUrl($product->image_path, $product->company_id),
                    'is_custom' => $product->is_custom,
                    'sequence' => $product->sequence,
                    'product_category_name' => $product->product_category,
                    // For backward compatibility, keep single-date string using provided date or today
                    'menu_items' => $this->formatMenuItems($product->items, $mealType, $kitchenCode, $date),
                    // When a range is provided, also include a per-date mapping
                    'menu_items_by_date' => ($fromDate && $toDate)
                        ? $this->formatMenuMetaByDateRange($product->items, $mealType, $kitchenCode, $fromDate, $toDate)
                        : null,
                ];
            })->values();

            if ($mealTypeProducts->isNotEmpty()) {
                // Determine display label with company-specific overrides
                $mealTypeDisplay = ucfirst($mealType);
                if ($companyId === 8163) {
                    if (strtolower($mealType) === 'breakfast') {
                        $mealTypeDisplay = 'Short Break';
                    } elseif (strtolower($mealType) === 'lunch') {
                        $mealTypeDisplay = 'Long Break';
                    }
                }
                $result[] = [
                    'meal_type' => $mealType,
                    'meal_type_display' => $mealTypeDisplay,
                    'menu_display_name' => $mealTypeDisplay,
                    'product_count' => $mealTypeProducts->count(),
                    'products' => $mealTypeProducts->toArray(),
                ];
            }
        }

        return $result;
    }

    /**
     * Build per-date metadata including items, disable flag, and express charges.
     * Returns: [ 'Y-m-d' => ['items' => string, 'disable' => bool, 'charges' => int], ... ]
     */
    private function formatMenuMetaByDateRange(?string $items, string $menuType, int $kitchenCode, string $fromDate, string $toDate): array
    {
        $out = [];
        // First compute the per-date items using existing logic for serving dates
        $itemsByDate = $this->formatMenuItemsByDateRange($items, $menuType, $kitchenCode, $fromDate, $toDate);

        // Determine timezone
        $tz = config('app.timezone') ?: env('APP_TIMEZONE', 'UTC');
        $now = Carbon::now($tz);
        $today = $now->copy()->startOfDay();

        // Resolve cutoffs for the given meal type
        $cutoffStr = $this->getCutoffTimeFor($kitchenCode, $menuType); // e.g. 10:30
        $cutoffToday = null;
        if ($cutoffStr) {
            try {
                [$h, $m] = array_pad(explode(':', $cutoffStr), 2, '0');
                $cutoffToday = $today->copy()->setTime((int)$h, (int)$m, 0);
            } catch (\Throwable $e) {
                $cutoffToday = null;
            }
        }


        // Express charge amount (0 if not configured) - kitchen & meal specific
        $expressAmount = $this->getExpressChargeAmount($kitchenCode, $menuType);

        // Express window start/end are settings-driven
        // Consider kitchen-specific, alias-specific, per-meal, and generic keys
        $mealUp = strtoupper($menuType);
        $aliasUp = strtoupper($this->getKitchenAlias($kitchenCode));
        $expressStartStr = $this->fetchSettingFlexible(array_values(array_filter([
            'K' . $kitchenCode . '_' . $mealUp . '_EXPRESS_WINDOW_START',
            $aliasUp ? ($aliasUp . '_' . $mealUp . '_EXPRESS_WINDOW_START') : null,
            $mealUp . '_EXPRESS_WINDOW_START',
            'EXPRESS_WINDOW_START',
        ])));
        $expressEndStr = $this->fetchSettingFlexible(array_values(array_filter([
            'K' . $kitchenCode . '_' . $mealUp . '_EXPRESS_EXTENDED_END_TIME',
            $aliasUp ? ($aliasUp . '_' . $mealUp . '_EXPRESS_EXTENDED_END_TIME') : null,
            $mealUp . '_EXPRESS_EXTENDED_END_TIME',
            'EXPRESS_EXTENDED_END_TIME',
        ])));

        // Feature gate: Only enable this custom express logic when company_id = 8163 AND EXPRESS_EXTENDED_ENABLED is truthy
        $companyId = (int) (request()->get('company_id') ?? 0);
        $enabledStr = $this->fetchSettingFlexible(array_values(array_filter([
            'K' . $kitchenCode . '_' . $mealUp . '_EXPRESS_EXTENDED_ENABLED',
            $aliasUp ? ($aliasUp . '_' . $mealUp . '_EXPRESS_EXTENDED_ENABLED') : null,
            $mealUp . '_EXPRESS_EXTENDED_ENABLED',
            'EXPRESS_EXTENDED_ENABLED',
        ])));
        $enabledNorm = strtolower(trim((string) $enabledStr));
        $expressFeatureOn = ($companyId === 8163) && in_array($enabledNorm, ['1','true','yes','on'], true);

        $expressStart = null;
        $expressEnd = null;
        try {
            $expressStart = $this->parseTimeToToday($today, $expressStartStr);
            $expressEnd   = $this->parseTimeToToday($today, $expressEndStr);
        } catch (\Throwable $e) {
            $expressStart = null;
            $expressEnd = null;
        }

        // If start is not configured but end is, default start to cutoff time when present
        // This lets non-breakfast orders be accepted after cutoff until express end.
        if ($expressStart === null && $expressEnd instanceof Carbon && $cutoffToday instanceof Carbon) {
            $expressStart = $cutoffToday->copy();
        }

        // Meal classification used in window/charge logic
        $isBreakfast = (strtoupper($menuType) === 'BREAKFAST');

        foreach ($itemsByDate as $date => $itemStr) {
            $dateObj = Carbon::createFromFormat('Y-m-d', $date, $tz)->startOfDay();

            // Debug: log values for current day to verify express window and cutoff resolution
            if ($dateObj->eq($today)) {
                Log::info('product_menu.express_window_debug', [
                    'kitchen_code' => $kitchenCode,
                    'meal_type' => $menuType,
                    'now' => $now->toDateTimeString(),
                    'cutoff_str' => $cutoffStr,
                    'cutoff_today' => $cutoffToday instanceof Carbon ? $cutoffToday->toDateTimeString() : null,
                    'express_start_str' => $expressStartStr,
                    'express_end_str' => $expressEndStr,
                    'express_start' => $expressStart instanceof Carbon ? $expressStart->toDateTimeString() : null,
                    'express_end' => $expressEnd instanceof Carbon ? $expressEnd->toDateTimeString() : null,
                    'is_breakfast' => $isBreakfast,
                    'in_window' => (!$isBreakfast)
                        && ($expressEnd instanceof Carbon)
                        && (
                            ($expressStart instanceof Carbon ? $now->gte($expressStart) : true)
                        )
                        && $now->lte($expressEnd),
                    'disable' => $dateObj->lt($today) || $dateObj->gt($today) || ($cutoffToday instanceof Carbon && $now->greaterThan($cutoffToday)),
                    'express_amount' => $expressAmount,
                ]);
            }

            // Disable rules
            if ($dateObj->lt($today)) {
                // Past dates: disabled
                $disable = true;
            } elseif ($dateObj->gt($today)) {
                // Future dates: enabled by default (allow ordering for future serving dates)
                // Note: itemsByDate already respects serving rules (weekoffs/holidays),
                // so future serving dates will be enabled unless other business rules apply.
                $disable = false;
            } else {
                // Today:
                // Determine express window membership (only for non-breakfast meals)
                // If start missing but end present, treat start as cutoff (handled above)
                $inExpressWindow = (!$isBreakfast)
                    && $expressFeatureOn
                    && ($expressEnd instanceof Carbon)
                    && (
                        ($expressStart instanceof Carbon ? $now->gte($expressStart) : true)
                    )
                    && $now->lte($expressEnd);

                // If within express window for non-breakfast meals, enable
                if ($inExpressWindow) {
                    $disable = false;
                } else {
                    // Otherwise, use cutoff/end-time rules
                    if ($cutoffToday instanceof Carbon) {
                        // After cutoff is disabled
                        $disable = $now->greaterThan($cutoffToday);
                    } else {
                        // No cutoff configured: if we have an express end time and it's passed, disable; else enable
                        if ($expressEnd instanceof Carbon && $now->greaterThan($expressEnd)) {
                            $disable = true;
                        } else {
                            $disable = false;
                        }
                    }
                }
            }

            // Express charge applies only for current day within window (non-breakfast)
            $charges = 0;
            if (
                $expressAmount > 0
                && !$isBreakfast
                && $dateObj->eq($today)
                && $expressFeatureOn
                && ($expressEnd instanceof Carbon)
                && (($expressStart instanceof Carbon) ? $now->gte($expressStart) : true)
                && $now->lte($expressEnd)
            ) {
                $charges = $expressAmount;
            }

            $out[$date] = [
                'items' => $itemStr,
                'disable' => $disable,
                'charges' => $charges,
            ];
        }

        return $out;
    }

    // --- Settings helpers ---
    private function getKitchenAlias(int $kitchenCode): string
    {
        try {
            $alias = DB::table('kitchens')->where('pk_kitchen_code', $kitchenCode)->value('alias');
            if ($alias && trim($alias) !== '') {
                return $alias;
            }
        } catch (\Throwable $e) {
            // ignore
        }
        return 'K' . $kitchenCode;
    }

    private function fetchSettingFlexible(array $keys): ?string
    {
        try {
            // 1) Try via Eloquent model scope if available (respects model connection/scopes)
            foreach ($keys as $candidate) {
                try {
                    if (method_exists(\App\Models\Setting::class, 'byKey')) {
                        $row = \App\Models\Setting::byKey($candidate)->first();
                        if ($row) {
                            foreach (['value','setting_value','config_value','val'] as $vcol) {
                                if (isset($row->{$vcol}) && $row->{$vcol} !== null && $row->{$vcol} !== '') {
                                    return (string)$row->{$vcol};
                                }
                            }
                            // As a last resort, if the model defines getAttribute('value')
                            $val = $row->getAttribute('value');
                            if ($val !== null && $val !== '') {
                                return (string)$val;
                            }
                        }
                    }
                } catch (\Throwable $ignored) {
                    // fall through to raw DB lookup
                }
            }

            // 2) Fallback: raw DB lookup with flexible key/value columns
            $keyColumns = array_values(array_filter([
                Schema::hasColumn('settings', 'key') ? 'key' : null,
                Schema::hasColumn('settings', 'setting_key') ? 'setting_key' : null,
                Schema::hasColumn('settings', 'name') ? 'name' : null,
                Schema::hasColumn('settings', 'config_key') ? 'config_key' : null,
                Schema::hasColumn('settings', 'setting_type') ? 'setting_type' : null,
            ]));
            $valueColumns = array_values(array_filter([
                Schema::hasColumn('settings', 'value') ? 'value' : null,
                Schema::hasColumn('settings', 'setting_value') ? 'setting_value' : null,
                Schema::hasColumn('settings', 'val') ? 'val' : null,
                Schema::hasColumn('settings', 'config_value') ? 'config_value' : null,
            ]));
            if (empty($keyColumns) || empty($valueColumns) || empty($keys)) {
                return null;
            }
            foreach ($keys as $candidate) {
                $q = DB::table('settings');
                $first = true;
                foreach ($keyColumns as $col) {
                    if ($first) {
                        $q->where($col, $candidate);
                        $first = false;
                    } else {
                        $q->orWhere($col, $candidate);
                    }
                }
                $row = $q->first();
                if ($row) {
                    foreach ($valueColumns as $vcol) {
                        if (isset($row->{$vcol}) && $row->{$vcol} !== null && $row->{$vcol} !== '') {
                            return (string)$row->{$vcol};
                        }
                    }
                }
            }
        } catch (\Throwable $e) {
            // ignore
        }
        return null;
    }

    private function getCutoffTimeFor(int $kitchenCode, string $mealType): ?string
    {
        $alias = strtoupper($this->getKitchenAlias($kitchenCode));
        $meal  = strtoupper(trim($mealType));
        $candidates = [
            // Most specific first (kitchen specific)
            'K' . $kitchenCode . '_' . $meal . '_ORDER_CUT_OFF_TIME',
            'K' . $kitchenCode . '_' . $meal . '_CUTOFF_TIME',
            'K' . $kitchenCode . '_' . $meal . '_CUTOFF',
            // Alias-based (if present in settings)
            $alias ? ($alias . '_' . $meal . '_ORDER_CUT_OFF_TIME') : null,
            $alias ? ($alias . '_' . $meal . '_CUTOFF_TIME') : null,
            $alias ? ($alias . '_' . $meal . '_CUTOFF') : null,
            // Generic fallbacks
            $meal . '_ORDER_CUT_OFF_TIME',
            $meal . '_CUTOFF_TIME',
            $meal . '_CUTOFF',
        ];
        // Remove nulls
        $candidates = array_values(array_filter($candidates));

        $val = $this->fetchSettingFlexible($candidates);
        if ($val !== null) {
            $raw = trim((string)$val);

            // Accept an optional day indicator: e.g. "0|10:30", "0 10:30", "0-10:30", "0,10:30"
            if (preg_match('/^\s*(\d+)\D+(\d{1,2}:\d{2})\s*$/', $raw, $m)) {
                // $day = (int)$m[1]; // day=0 means same day (as per requirement)
                return $m[2]; // return HH:MM only
            }

            // Normalize common separators to ':'
            $norm = str_replace(['.', '-'], ':', $raw);
            // Already HH:MM
            if (preg_match('/^(\d{1,2}):(\d{2})$/', $norm)) {
                return $norm;
            }
            // Compact 4-digit like 1030 or 930
            $digits = preg_replace('/[^0-9]/', '', $raw);
            if (strlen($digits) === 3) { // e.g. 930 -> 0930
                $digits = '0' . $digits;
            }
            if (strlen($digits) === 4) {
                $h = (int)substr($digits, 0, 2);
                $mi = (int)substr($digits, 2, 2);
                $h = max(0, min(23, $h));
                $mi = max(0, min(59, $mi));
                return sprintf('%02d:%02d', $h, $mi);
            }
        }
        return null;
    }

    private function getExpressChargeAmount(int $kitchenCode, string $mealType): int
    {
        $alias = strtoupper($this->getKitchenAlias($kitchenCode));
        $meal  = strtoupper(trim($mealType));

        // Prefer kitchen/meal specific keys for ANY meal (including breakfast)
        $candidates = [
            'K' . $kitchenCode . '_' . $meal . '_EXPRESS_EXTRA_DELIVERY_CHARGE',
            // Also try alias based if present
            $alias ? ($alias . '_' . $meal . '_EXPRESS_EXTRA_DELIVERY_CHARGE') : null,
            // Generic per-meal fallback
            $meal . '_EXPRESS_EXTRA_DELIVERY_CHARGE',
            // Global fallback
            'EXPRESS_EXTRA_DELIVERY_CHARGE',
        ];
        $candidates = array_values(array_filter($candidates));

        $val = $this->fetchSettingFlexible($candidates);
        if ($val !== null && $val !== '') {
            $n = (int) filter_var($val, FILTER_SANITIZE_NUMBER_INT);
            if ($n > 0) {
                return $n;
            }
        }

        // If no specific value found:
        // - Breakfast defaults to 0 (no charges) unless a specific key is set above
        if ($meal === 'BREAKFAST') {
            return 0;
        }

        // - Others (e.g., Lunch): fall back to conventional key 'c'/'C' (e.g., 40)
        $fallbackVal = $this->fetchSettingFlexible(['c', 'C']);
        if ($fallbackVal !== null && $fallbackVal !== '') {
            $n = (int) filter_var($fallbackVal, FILTER_SANITIZE_NUMBER_INT);
            return $n > 0 ? $n : 0;
        }

        return 0;
    }

    private function assetUrl($imagePath, $companyId){
        if(empty($imagePath) || empty($companyId)){
            return null;
        }
        
        $region = config('filesystems.disks.s3.region');
        $bucket = config('filesystems.disks.s3.bucket');

        return 'https://s3.'.$region.'.amazonaws.com/'.$bucket.'/'.$companyId.'/product/'.$imagePath;
    }

    /**
     * Parse a time string into a Carbon instance on the provided $today date.
     * Supports formats: HH:MM, HH:MM:SS, H:MM, 930/0930/1900, 7 PM, 07:00 PM, and separators '.' or '-'.
     */
    private function parseTimeToToday(Carbon $today, $raw): ?Carbon
    {
        if ($raw === null || $raw === '') return null;
        try {
            $t = trim((string)$raw);
            // Normalize separators
            $tNorm = str_replace(['.', '-'], ':', $t);

            // HH:MM or HH:MM:SS
            if (preg_match('/^(\d{1,2}):(\d{2})(?::(\d{2}))?$/', $tNorm, $m)) {
                $h = max(0, min(23, (int)$m[1]));
                $mi = max(0, min(59, (int)$m[2]));
                $s = isset($m[3]) ? max(0, min(59, (int)$m[3])) : 0;
                return $today->copy()->setTime($h, $mi, $s);
            }

            // Compact 3/4 digit like 930, 0930, 1900
            $digits = preg_replace('/\D+/', '', $t);
            if (strlen($digits) === 3) {
                $digits = '0' . $digits;
            }
            if (strlen($digits) === 4) {
                $h = max(0, min(23, (int)substr($digits, 0, 2)));
                $mi = max(0, min(59, (int)substr($digits, 2, 2)));
                return $today->copy()->setTime($h, $mi, 0);
            }

            // With AM/PM: "7 PM", "07:00 PM"
            if (preg_match('/^(\d{1,2})\s*(am|pm)$/i', $tNorm, $m)) {
                $h12 = (int)$m[1];
                $ampm = strtolower($m[2]);
                $h = $h12 % 12 + ($ampm === 'pm' ? 12 : 0);
                return $today->copy()->setTime($h, 0, 0);
            }
            if (preg_match('/^(\d{1,2}):(\d{2})\s*(am|pm)$/i', $tNorm, $m)) {
                $h12 = (int)$m[1];
                $mi = (int)$m[2];
                $ampm = strtolower($m[3]);
                $h = $h12 % 12 + ($ampm === 'pm' ? 12 : 0);
                $mi = max(0, min(59, $mi));
                return $today->copy()->setTime($h, $mi, 0);
            }
        } catch (\Throwable $e) {
            // ignore
        }
        return null;
    }

    /**
     * Format menu items from JSON string to readable format
     * Uses product_planner table first, then fallbacks to products table
     * Similar to the logic used in quickserve service for meal_items
     *
     * @param string|null $items
     * @param string $menuType
     * @return string
     */
    private function formatMenuItems(?string $items, string $menuType = '', int $kitchenCode = 0, ?string $date = null): string
    {
        if (empty($items)) {
            return '';
        }

        try {
            // Try to decode as JSON first (if items are stored as JSON)
            $decodedItems = json_decode($items, true);

            if (json_last_error() === JSON_ERROR_NONE && is_array($decodedItems)) {
                // Extract product IDs from the JSON object
                $productIds = array_keys($decodedItems);

                if (!empty($productIds)) {
                    $itemNames = [];
                    $useDate = $date ?: now()->format('Y-m-d');

                    foreach ($productIds as $productId) {
                        $itemName = $this->getItemNameFromPlanner($productId, $menuType, $useDate, $kitchenCode);
                        if ($itemName && $this->isValidMenuItemName($itemName)) {
                            $itemNames[] = $itemName;
                        }
                    }

                    return implode(', ', $itemNames);
                }
            }
        } catch (\Exception $e) {
            // If JSON decode fails or database query fails, treat as plain text
        }

        // If not JSON or JSON decode failed, treat as comma-separated string
        // Clean up the string and return as is
        $raw = trim($items);
        // Attempt to filter out any 'None*' items in a comma-separated list
        $parts = array_map('trim', explode(',', $raw));
        $parts = array_values(array_filter($parts, function($name){
            return $this->isValidMenuItemName($name);
        }));
        return implode(', ', $parts);
    }

    /**
     * Build a mapping of date => comma separated item names using product_planner when available
     * Falls back to products table for unknown items.
     * Returns an associative array keyed by date (Y-m-d) or empty array when items invalid.
     */
    private function formatMenuItemsByDateRange(?string $items, string $menuType, int $kitchenCode, string $fromDate, string $toDate): array
    {
        if (empty($items)) {
            return [];
        }

        $decodedItems = json_decode($items, true);
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($decodedItems)) {
            // If items are not JSON, filter and return the same string for all serving dates in range
            $raw = trim($items);
            $parts = array_map('trim', explode(',', $raw));
            $parts = array_values(array_filter($parts, function($name){
                return $this->isValidMenuItemName($name);
            }));
            $filtered = implode(', ', $parts);

            $out = [];
            for ($date = $fromDate; $date <= $toDate; $date = date('Y-m-d', strtotime($date . ' +1 day'))) {
                if (!$this->isServingDate($date, $kitchenCode, $menuType)) {
                    continue;
                }
                $out[$date] = $filtered;
            }
            return $out;
        }

        $productIds = array_keys($decodedItems);
        if (empty($productIds)) {
            return [];
        }

        $result = [];
        for ($date = $fromDate; $date <= $toDate; $date = date('Y-m-d', strtotime($date . ' +1 day'))) {
            // Skip non-serving dates per settings (weekoffs) and holidays
            if (!$this->isServingDate($date, $kitchenCode, $menuType)) {
                continue;
            }

            $names = [];
            foreach ($productIds as $productId) {
                $name = $this->getItemNameFromPlanner((int)$productId, $menuType, $date, $kitchenCode);
                if ($name && $this->isValidMenuItemName($name)) {
                    $names[] = $name;
                }
            }
            $result[$date] = implode(', ', $names);
        }

        return $result;
    }

    /**
     * Validate menu item name - exclude those starting with 'None'
     */
    private function isValidMenuItemName(string $name): bool
    {
        $trimmed = trim($name);
        if ($trimmed === '') {
            return false;
        }
        // Case-insensitive check for names starting with 'None'
        return stripos($trimmed, 'None') !== 0;
    }

    /**
     * Determine if kitchen serves on a given date for a given meal type, considering weekoffs and holidays.
     */
    private function isServingDate(string $date, int $kitchenCode, string $mealType): bool
    {
        // Weekday number where 0 (for Sunday) through 6 (for Saturday)
        $weekday = (int) date('w', strtotime($date));
        $weekoffs = $this->getWeekOffsFor($kitchenCode, strtolower($mealType));
        if (in_array($weekday, $weekoffs, true)) {
            return false;
        }

        // Check holidays (kitchen-aware when possible)
        if ($this->isHoliday($date, $kitchenCode)) {
            return false;
        }

        return true;
    }

    /**
     * Get weekoffs array (e.g., [0,6]) from settings table for the kitchen and meal type.
     * Keys format: {ALIAS}_{MEAL}_WEEKOFFS, e.g., K1_LUNCH_WEEKOFFS
     */
    private function getWeekOffsFor(int $kitchenCode, string $mealType): array
    {
        try {
            // Resolve kitchen alias; fallback to 'K{code}'
            $alias = DB::table('kitchens')
                ->where('pk_kitchen_code', $kitchenCode)
                ->value('alias');
            if (empty($alias)) {
                $alias = 'K' . $kitchenCode;
            }

            $mealUpper = strtoupper($mealType);
            $candidates = [
                strtoupper($alias . '_' . $mealUpper . '_WEEKOFFS'),
                'K' . $kitchenCode . '_' . $mealUpper . '_WEEKOFFS',
                $mealUpper . '_WEEKOFFS',
            ];

            // Determine possible key and value columns in settings
            $keyColumns = array_values(array_filter([
                Schema::hasColumn('settings', 'key') ? 'key' : null,
                Schema::hasColumn('settings', 'setting_key') ? 'setting_key' : null,
                Schema::hasColumn('settings', 'name') ? 'name' : null,
                Schema::hasColumn('settings', 'config_key') ? 'config_key' : null,
            ]));

            $valueColumns = array_values(array_filter([
                Schema::hasColumn('settings', 'value') ? 'value' : null,
                Schema::hasColumn('settings', 'setting_value') ? 'setting_value' : null,
                Schema::hasColumn('settings', 'val') ? 'val' : null,
                Schema::hasColumn('settings', 'config_value') ? 'config_value' : null,
            ]));

            $value = null;
            if (!empty($keyColumns) && !empty($valueColumns)) {
                foreach ($candidates as $candidate) {
                    $q = DB::table('settings');
                    $first = true;
                    foreach ($keyColumns as $col) {
                        if ($first) {
                            $q->where($col, $candidate);
                            $first = false;
                        } else {
                            $q->orWhere($col, $candidate);
                        }
                    }
                    $row = $q->first();
                    if ($row) {
                        foreach ($valueColumns as $vcol) {
                            if (isset($row->{$vcol}) && $row->{$vcol} !== null) {
                                $value = $row->{$vcol};
                                break 2;
                            }
                        }
                    }
                }
            }

            if (is_string($value) && trim($value) !== '') {
                $parts = array_map('trim', explode(',', $value));
                // Convert to integers 0..6, filter invalids
                $ints = array_values(array_filter(array_map(function ($p) {
                    if ($p === '') return null;
                    $n = (int) $p;
                    return ($n >= 0 && $n <= 6) ? $n : null;
                }, $parts), function ($v) {
                    return $v !== null;
                }));
                return $ints;
            }
        } catch (\Throwable $e) {
            // Fail-safe: ignore and treat as no weekoffs
        }

        // Sensible default: Saturday(6) and Sunday(0) are off if not configured
        return [0, 6];
    }

    /**
     * Check holiday_master for a holiday on the given date.
     */
    private function isHoliday(string $date, ?int $kitchenCode = null): bool
    {
        try {
            // Determine possible date columns
            $dateColumns = array_values(array_filter([
                Schema::hasColumn('holiday_master', 'date') ? 'date' : null,
                Schema::hasColumn('holiday_master', 'holiday_date') ? 'holiday_date' : null,
                Schema::hasColumn('holiday_master', 'dt') ? 'dt' : null,
            ]));

            if (empty($dateColumns)) {
                return false;
            }

            // Build base query matching any date column to the provided date
            $query = DB::table('holiday_master');
            $first = true;
            foreach ($dateColumns as $col) {
                if ($first) {
                    $query->where($col, $date);
                    $first = false;
                } else {
                    $query->orWhere($col, $date);
                }
            }

            // Scope by kitchen if a kitchen column exists and kitchenCode provided
            if ($kitchenCode !== null) {
                $kitchenCols = array_values(array_filter([
                    Schema::hasColumn('holiday_master', 'fk_kitchen_code') ? 'fk_kitchen_code' : null,
                    Schema::hasColumn('holiday_master', 'kitchen_code') ? 'kitchen_code' : null,
                    Schema::hasColumn('holiday_master', 'kitchen') ? 'kitchen' : null,
                    Schema::hasColumn('holiday_master', 'fk_kitchen') ? 'fk_kitchen' : null,
                    Schema::hasColumn('holiday_master', 'kitchen_id') ? 'kitchen_id' : null,
                ]));
                foreach ($kitchenCols as $kcol) {
                    $query->where($kcol, $kitchenCode);
                }
            }

            // Apply active/status flags if present
            if (Schema::hasColumn('holiday_master', 'is_active')) {
                $query->where('is_active', 1);
            }
            if (Schema::hasColumn('holiday_master', 'active')) {
                $query->where('active', 1);
            }
            if (Schema::hasColumn('holiday_master', 'status')) {
                $query->where('status', 1);
            }
            if (Schema::hasColumn('holiday_master', 'is_deleted')) {
                $query->where('is_deleted', 0);
            }

            return $query->exists();
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * Get item name from product_planner table first, fallback to products table
     * Follows the same logic as quickserve service
     *
     * @param int $itemCode
     * @param string $menuType
     * @param string $date
     * @return string|null
     */
    private function getItemNameFromPlanner(int $itemCode, string $menuType, string $date, int $kitchenCode): ?string
    {
        try {
            // First check product_planner table for specific product name
            $plannerItem = DB::table('product_planner')
                ->where('date', $date)
                ->where('menu', strtolower($menuType))
                ->where('fk_kitchen_code', $kitchenCode)
                ->where('generic_product_code', $itemCode)
                ->first(['specific_product_code', 'specific_product_name']);

            if ($plannerItem && !empty($plannerItem->specific_product_name)) {
                return $plannerItem->specific_product_name;
            }

            // Fallback to products table
            $product = Product::where('pk_product_code', $itemCode)
                ->first(['name']);

            if ($product) {
                return $product->name;
            }

            return 'Unknown Item';

        } catch (\Exception $e) {
            // If database query fails, fallback to products table
            $product = Product::where('pk_product_code', $itemCode)
                ->first(['name']);

            return $product ? $product->name : 'Unknown Item';
        }
    }

    /**
     * Get active delivery location based on city and/or delivery location parameters
     * (Reused from MenuController)
     */
    private function getActiveDeliveryLocation(?string $city, ?string $deliveryLocation, ?int $deliveryLocationId, int $companyId): ?\App\Models\DeliveryLocation
    {
        // Priority 1: Use delivery_location_id if provided
        if ($deliveryLocationId) {
            return DeliveryLocation::byCompany($companyId)
                                 ->where('pk_location_code', $deliveryLocationId)
                                 ->where('status', 1)
                                 ->first();
        }

        $query = DeliveryLocation::byCompany($companyId)->where('status', 1);

        // Priority 2: Use delivery_location name with optional city filter
        if ($deliveryLocation) {
            $query->where('location', 'LIKE', '%' . $deliveryLocation . '%');

            if ($city) {
                $query->where('city', 'LIKE', '%' . $city . '%');
            }

            return $query->first();
        }

        // Priority 3: Use city only
        if ($city) {
            return $query->where('city', 'LIKE', '%' . $city . '%')->first();
        }

        return $query->first();
    }
    private function getKitchenData(DeliveryLocation $deliveryLocation): array
    {
        $kitchenCode = $deliveryLocation->fk_kitchen_code;

        if (!$kitchenCode) {
            return $this->buildErrorResponse(
                'No kitchen code found for the delivery location',
                404,
                [
                    'delivery_location_id' => $deliveryLocation->pk_location_code,
                    'delivery_location_name' => $deliveryLocation->location,
                ]
            );
        }

        $kitchen = KitchenMaster::where('pk_kitchen_code', $kitchenCode)->first();

        if (!$kitchen || !$kitchen->kitchen_alias) {
            return $this->buildErrorResponse(
                'No kitchen alias found for kitchen code',
                404,
                [
                    'kitchen_code' => $kitchenCode,
                    'delivery_location_id' => $deliveryLocation->pk_location_code,
                ]
            );
        }

        return [
            'success' => true,
            'kitchen' => $kitchen,
        ];
    }

    /**
     * Helper method to build error responses
     */
    private function buildErrorResponse(string $message, int $statusCode, array $data = []): array
    {
        return [
            'success' => false,
            'message' => $message,
            'status_code' => $statusCode,
            'data' => $data,
        ];
    }

    /**
     * Helper method for validation error responses
     */
    private function validationErrorResponse($errors): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors,
        ], 422);
    }

    /**
     * Helper method for exception responses
     */
    private function exceptionResponse(\Exception $e, string $message, array $requestData = []): JsonResponse
    {
        Log::error($message, [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'request_data' => $requestData,
        ]);

        return response()->json([
            'success' => false,
            'message' => 'Failed to process request',
            'error' => $e->getMessage(),
        ], 500);
    }
}
