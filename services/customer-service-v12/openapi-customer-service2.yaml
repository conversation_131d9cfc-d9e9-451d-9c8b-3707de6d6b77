openapi: 3.0.3
info:
  title: OneFoodDialer 2025 - Customer Service API (JWT Protected - Complete)
  description: |
    Comprehensive Customer Service API for OneFoodDialer 2025 platform with complete JWT authentication coverage.

    ## Authentication
    All endpoints require JWT authentication via Bearer token using jwt.auth middleware.

    Include the JWT token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```

    ## Complete Feature Set
    - Customer Management: Registration, authentication, and profile management
    - Customer Address Management: Full CRUD operations with student profile support
    - Product Menu: Dynamic menu retrieval based on kitchen types and categories
    - Wallet Management: Complete wallet operations (balance, deposit, withdraw, transfer, transactions)
    - Delivery Locations: School and location management
    - JWT Security: Role-based access control with Keycloak integration

    ## Student Profile Format
    For customer addresses with menu_type school, student details are stored in location_address field:
    ```
    "Child Name, Class, Division, Floor, Allergies"
    ```
    Where allergies are separated by " - " (e.g., "Wheat - Lactose - Nuts")

    ## Database Configuration
    - Database: live_quickserve_8163
    - Company ID: 8163 (configured via environment)
    - Scope: All operations are automatically filtered by company_id

    ## Middleware Configuration
    - JWT Middleware: jwt.auth (registered as KeycloakJwtAuth::class)
    - Token Validation: Keycloak JWT tokens with role-based access
    - Error Handling: Comprehensive error responses with proper HTTP status codes

    ## API Coverage
    This specification includes ALL JWT-protected endpoints from the customer service:
    - Customer & Address endpoints (CRUD operations)
    - Wallet Management endpoints (complete wallet operations)
    - Product & Delivery endpoints (menu and location management)

    ## Ready for Production
    - Validated OpenAPI 3.0.3 specification
    - Complete request/response schemas with examples
    - Proper error handling for all scenarios
    - Import-ready for Postman, Insomnia, Swagger UI
  version: 2.2.1
  contact:
    name: OneFoodDialer 2025 API Support
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://*************:8000
    description: Proxy server
  - url: http://**************:9001/api
    description: Production server
  - url: http://************:8000/api
    description: Local server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"
        errors:
          type: object
          additionalProperties: true

    CustomerAddress:
      type: object
      properties:
        pk_customer_address_code:
          type: integer
          example: 123
        fk_customer_code:
          type: integer
          example: 3787
        location_address:
          type: string
          example: "John Doe, 5th Grade, A, Ground Floor, Wheat - Lactose"
        location_code:
          type: integer
          example: 101
        menu_type:
          type: string
          example: "school"
        city:
          type: string
          example: "Mumbai"
        is_default:
          type: integer
          example: 1
        status:
          type: integer
          example: 1
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        student_profile:
          type: object
          nullable: true
          properties:
            child_name:
              type: string
            class:
              type: string
            division:
              type: string
            floor:
              type: string
            allergies:
              type: array
              items:
                type: string

    CustomerAddressResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Customer address retrieved successfully"
        data:
          $ref: '#/components/schemas/CustomerAddress'

    CustomerAddressListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Customer addresses retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/CustomerAddress'

    DeliveryLocation:
      type: object
      properties:
        pk_location_code:
          type: integer
          example: 101
        location:
          type: string
          example: "ABC International School"
        city:
          type: string
          example: "Mumbai"
        status:
          type: integer
          example: 1
        fk_company_code:
          type: integer
          example: 8163
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    DeliveryLocationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Delivery location retrieved successfully"
        data:
          $ref: '#/components/schemas/DeliveryLocation'

    DeliveryLocationListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Delivery locations retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/DeliveryLocation'

    Product:
      type: object
      properties:
        pk_product_code:
          type: integer
          example: 123
        name:
          type: string
          example: "Breakfast Combo"
        unit_price:
          type: number
          format: float
          example: 150.00
        category:
          type: string
          example: "breakfast"
        food_type:
          type: string
          example: "veg"
        status:
          type: integer
          example: 1

    ProductCategory:
      type: object
      properties:
        pk_product_category_code:
          type: integer
          example: 1
        category_name:
          type: string
          example: "breakfast"
        category_type:
          type: string
          example: "meal"
        status:
          type: integer
          example: 1

    Kitchen:
      type: object
      properties:
        pk_kitchen_code:
          type: integer
          example: 1
        kitchen_alias:
          type: string
          example: "main_kitchen"
        kitchen_name:
          type: string
          example: "Main Kitchen"
        status:
          type: integer
          example: 1

    ProductMenuResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Product menu retrieved successfully"
        data:
          type: object
          properties:
            delivery_location:
              $ref: '#/components/schemas/DeliveryLocation'
            kitchen:
              $ref: '#/components/schemas/Kitchen'
            menu_type:
              type: string
              example: "breakfast,lunch"
            product_categories:
              type: array
              items:
                $ref: '#/components/schemas/ProductCategory'
            products_by_category:
              type: object
              additionalProperties:
                type: array
                items:
                  $ref: '#/components/schemas/Product'

    WalletDetails:
      type: object
      properties:
        customer_id:
          type: integer
          example: 1543
        customer_code:
          type: integer
          example: 1543
        balance:
          type: number
          format: float
          example: 367.5
        available_balance:
          type: number
          format: float
          example: 367.5
        locked_balance:
          type: number
          format: float
          example: 0
        total_credit:
          type: number
          format: float
          example: 16511.25
        total_debit:
          type: number
          format: float
          example: 16143.75
        currency:
          type: string
          example: INR
        status:
          type: string
          example: active

    WalletTransaction:
      type: object
      properties:
        id:
          type: integer
          example: 217555
        customer_code:
          type: integer
          example: 1543
        amount:
          type: number
          format: float
          example: 78.75
        type:
          type: string
          enum: [credit, debit, lock]
          example: debit
        description:
          type: string
          example: "Rs. 78.75 deducted against Bill No. 126413 of Order No. 3WAA250629"
        transaction_id:
          type: string
          example: "OMO2506291050393731401561"
        date:
          type: string
          format: date
          example: "2025-07-02"
        payment_type:
          type: string
          enum: [neft, cash, cheque, online, wallet, partial]
          example: wallet
        context:
          type: string
          enum: [admin, customer]
          example: admin
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    WalletTransactionsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            meta:
              type: object
              properties:
                customer_id:
                  type: integer
                  example: 1543
                total_balance:
                  type: number
                  example: 367.5
                available_balance:
                  type: number
                  example: 367.5
                usable_balance:
                  type: number
                  example: 367.5
                locked_balance:
                  type: number
                  example: 0
                currency:
                  type: string
                  example: INR
            history:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/WalletTransaction'
                pagination:
                  type: object
                  properties:
                    current_page:
                      type: integer
                      example: 1
                    per_page:
                      type: integer
                      example: 15
                    total:
                      type: integer
                      example: 50
                    last_page:
                      type: integer
                      example: 4

    WalletWithHistoryResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            wallet_details:
              $ref: '#/components/schemas/WalletDetails'
            wallet_history:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/WalletTransaction'
                pagination:
                  type: object
                  properties:
                    current_page:
                      type: integer
                      example: 1
                    per_page:
                      type: integer
                      example: 15
                    total:
                      type: integer
                      example: 50
                    last_page:
                      type: integer
                      example: 4

security:
  - bearerAuth: []

tags:
  - name: Customer Management
  - name: Customer Address
  - name: Product Menu
  - name: Wallet Management
  - name: Delivery Locations

paths:
  /v2/customers/find-or-create-keycloak:
    post:
      summary: Find or create customer from Keycloak authentication
      description: |
        Implements a find-or-create pattern using username (phone/email) and Keycloak auth_id.
      operationId: findOrCreateCustomerFromKeycloak
      tags: [Customer Management]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [username, auth_id, customer_name, phone, company_id]
              properties:
                username:
                  type: string
                  maxLength: 255
                auth_id:
                  type: string
                  maxLength: 255
                customer_name:
                  type: string
                  maxLength: 45
                email_address:
                  type: string
                  format: email
                  nullable: true
                phone:
                  type: string
                  maxLength: 15
                company_id:
                  type: integer
                unit_id:
                  type: integer
                  nullable: true
                customer_address:
                  type: string
                  maxLength: 200
                  nullable: true
                food_preference:
                  type: string
                  nullable: true
                  enum: [veg, non-veg, both]
                registered_from:
                  type: string
                  nullable: true
                source:
                  type: string
                  nullable: true
                thirdparty:
                  type: string
                  nullable: true
      responses:
        '200':
          description: Existing customer found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  action:
                    type: string
                    enum: [found]
                  data:
                    type: object
                    properties:
                      pk_customer_code:
                        type: integer
                      auth_id:
                        type: string
                      customer_name:
                        type: string
                      email_address:
                        type: string
                      phone:
                        type: string
                      company_id:
                        type: integer
                      unit_id:
                        type: integer
        '201':
          description: New customer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  action:
                    type: string
                    enum: [created]
                  data:
                    type: object
                    properties:
                      pk_customer_code:
                        type: integer
                      auth_id:
                        type: string
                      customer_name:
                        type: string
                      email_address:
                        type: string
                      phone:
                        type: string
                      company_id:
                        type: integer
                      unit_id:
                        type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customer-address:
    get:
      summary: Get customer addresses
      description: Retrieve a paginated list of customer addresses for a specific customer.
      operationId: getCustomerAddresses
      tags: [Customer Address]
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
        - name: customer_id
          in: query
          schema:
            type: integer
        - name: per_page
          in: query
          schema:
            type: integer
            default: 15
            minimum: 1
            maximum: 100
        - name: menu_type
          in: query
          schema:
            type: string
        - name: location_code
          in: query
          schema:
            type: integer
        - name: search
          in: query
          schema:
            type: string
        - name: city
          in: query
          schema:
            type: string
        - name: show_all
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Customer addresses retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressListResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customer-address/{id}:
    get:
      summary: Get specific customer address
      description: Retrieve detailed information about a specific customer address.
      operationId: getCustomerAddressById
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
        - name: customer_id
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: Customer address retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      summary: Update customer address
      description: Update a customer address by ID.
      operationId: updateCustomerAddress
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id]
              properties:
                customer_id:
                  type: integer
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                location_code:
                  type: integer
                child_name:
                  type: string
                class:
                  type: string
                division:
                  type: string
                floor:
                  type: string
                allergies:
                  type: array
                  items:
                    type: string
                default:
                  type: boolean
      responses:
        '200':
          description: Customer address updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      summary: Delete customer address
      description: Delete a customer address by ID.
      operationId: deleteCustomerAddress
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
        - name: customer_id
          in: query
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
      responses:
        '200':
          description: Customer address deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customer-addresses:
    post:
      summary: Create customer address (JWT Protected)
      description: Create a new customer address (student profile for school menu).
      operationId: createCustomerAddressJWT
      tags: [Customer Address]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id, location_code, child_name, class, division, floor]
              properties:
                customer_id:
                  type: integer
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                location_code:
                  type: integer
                child_name:
                  type: string
                class:
                  type: string
                division:
                  type: string
                floor:
                  type: string
                allergies:
                  type: array
                  items:
                    type: string
                menu_type:
                  type: string
                  enum: [school]
                city:
                  type: string
                default:
                  type: boolean
      responses:
        '201':
          description: Customer address created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customer-addresses/{id}:
    put:
      summary: Update customer address (JWT Protected)
      description: Update a customer address by ID.
      operationId: updateCustomerAddressJWT
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id]
              properties: { }
      responses:
        '200':
          description: Customer address updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      summary: Delete customer address (JWT Protected)
      description: Delete a customer address by ID.
      operationId: deleteCustomerAddressJWT
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
        - name: customer_id
          in: query
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
      responses:
        '200':
          description: Customer address deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customer-addresses/{id}/set-default:
    patch:
      summary: Set default customer address (JWT Protected)
      description: Set the specified address as default for the customer, scoped by company.
      operationId: setDefaultCustomerAddressJWT
      tags: [Customer Address]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, company_id]
              properties:
                customer_id:
                  type: integer
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
      responses:
        '200':
          description: Default address updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Customer address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/customer-addresses/selected:
    get:
      summary: Get selected customer address (JWT Protected)
      description: Returns the currently selected (default) address for the customer, scoped by company.
      operationId: getSelectedCustomerAddress
      tags: [Customer Address]
      parameters:
        - name: customer_id
          in: query
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
      responses:
        '200':
          description: Selected address fetched successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/product-menu:
    get:
      summary: Get product menu
      description: Retrieve active products/meals based on kitchen's menu type and categories.
      operationId: getProductMenu
      tags: [Product Menu]
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
        - name: city
          in: query
          schema:
            type: string
        - name: delivery_location
          in: query
          schema:
            type: string
        - name: delivery_location_id
          in: query
          schema:
            type: integer
            minimum: 1
        - name: from_date
          in: query
          schema:
            type: string
            format: date
        - name: to_date
          in: query
          schema:
            type: string
            format: date
        - name: date
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Product menu retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductMenuResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/delivery-locations:
    get:
      summary: Get delivery locations
      description: Retrieve a paginated list of delivery locations (schools) filtered by company.
      operationId: getDeliveryLocations
      tags: [Delivery Locations]
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
        - name: per_page
          in: query
          schema:
            type: integer
            default: 15
            minimum: 1
            maximum: 100
        - name: status
          in: query
          schema:
            type: integer
            enum: [0, 1]
        - name: search
          in: query
          schema:
            type: string
        - name: city
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Delivery locations retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryLocationListResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/delivery-locations/{id}:
    get:
      summary: Get specific delivery location
      description: Retrieve detailed information about a specific delivery location.
      operationId: getDeliveryLocationById
      tags: [Delivery Locations]
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 99999
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Delivery location retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryLocationResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/{customerId}:
    get:
      summary: Get customer wallet details
      description: Returns the wallet balance and details for a specific customer
      operationId: getCustomerWalletDirect
      tags: [Wallet Management]
      parameters:
        - name: customerId
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
        - name: company_id
          in: query
          description: Company ID for scoping and validation
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Wallet details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/WalletDetails'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/{customerId}/balance:
    get:
      summary: Get customer wallet balance
      description: Returns only the balance information for a specific customer's wallet
      operationId: getCustomerWalletBalanceDirect
      tags: [Wallet Management]
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Wallet balance retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      customer_id:
                        type: integer
                      balance:
                        type: number
                        format: float
                      currency:
                        type: string
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/{customerId}/transactions:
    get:
      summary: Get customer wallet transaction history
      description: Returns paginated transaction history for a customer's wallet
      operationId: getCustomerWalletTransactionsDirect
      tags: [Wallet Management]
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
        - name: type
          in: query
          schema:
            type: string
            enum: [credit, debit, lock]
        - name: date_from
          in: query
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          schema:
            type: string
            format: date
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 15
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
      responses:
        '200':
          description: Transaction history retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletTransactionsResponse'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/{customerId}/details:
    get:
      summary: Get wallet details with history (UI)
      description: Returns wallet details and paginated history ordered by timestamp desc
      operationId: getWalletWithHistoryDirect
      tags: [Wallet Management]
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 15
        - name: date_from
          in: query
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Wallet details with history retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletWithHistoryResponse'
        '404':
          description: Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/add:
    post:
      summary: Add funds to wallet (JWT Protected)
      description: Initiate wallet top-up by creating a payment request.
      operationId: addWalletFundsJWT
      tags: [Wallet Management]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, amount, company_id]
              properties:
                customer_id:
                  type: integer
                amount:
                  type: number
                  format: float
                  minimum: 1
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                transaction_id:
                  type: string
                  nullable: true
                payment_method:
                  type: string
                  enum: [razorpay, payu, manual]
                  nullable: true
                description:
                  type: string
                  maxLength: 255
                  nullable: true
      responses:
        '202':
          description: Payment initiated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      payment_id:
                        type: string
                      status:
                        type: string
                        example: pending
                      reference:
                        type: string
                      payment_urls:
                        type: object
                        properties:
                          process_payment:
                            type: string
                          payment_status:
                            type: string
                      amount:
                        type: number
                      currency:
                        type: string
                        example: INR
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/deduct:
    post:
      summary: Deduct funds from wallet (JWT Protected)
      description: Deduct funds from customer wallet for order payments or other transactions.
      operationId: deductWalletFundsJWT
      tags: [Wallet Management]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [customer_id, amount, company_id, description]
              properties:
                customer_id:
                  type: integer
                amount:
                  type: number
                  format: float
                  minimum: 1
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                description:
                  type: string
                  maxLength: 255
                order_id:
                  type: string
                  nullable: true
                reference_id:
                  type: string
                  nullable: true
      responses:
        '200':
          description: Withdrawal successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/WalletDetails'
        '400':
          description: Insufficient balance
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/transfer:
    post:
      summary: Transfer funds between wallets (JWT Protected)
      description: Transfer funds from one customer wallet to another within the same company.
      operationId: transferWalletFundsJWT
      tags: [Wallet Management]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [from_customer_id, to_customer_id, amount, company_id, description]
              properties:
                from_customer_id:
                  type: integer
                to_customer_id:
                  type: integer
                amount:
                  type: number
                  format: float
                  minimum: 1
                company_id:
                  type: integer
                  minimum: 1
                  maximum: 99999
                description:
                  type: string
                  maxLength: 255
                reference_id:
                  type: string
                  nullable: true
      responses:
        '200':
          description: Transfer completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      transfer_id:
                        type: integer
                      from_balance:
                        type: number
                      to_balance:
                        type: number
                      amount_transferred:
                        type: number
        '400':
          description: Transfer failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/history:
    get:
      summary: Get all wallet history (JWT Protected)
      description: Returns global wallet history (admin/reporting scope). Implementation may restrict usage.
      operationId: getAllWalletHistory
      tags: [Wallet Management]
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 15
      responses:
        '200':
          description: History retrieved
          content:
            application/json:
              schema:
                type: object
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/statistics:
    get:
      summary: Get wallet statistics (JWT Protected)
      description: Returns aggregate wallet statistics (admin/reporting scope).
      operationId: getWalletStatistics
      tags: [Wallet Management]
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Statistics retrieved
          content:
            application/json:
              schema:
                type: object
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/wallet/payments/callback:
    post:
      summary: Wallet payment callback (JWT Protected)
      description: Unified callback from payment-service for wallet top-ups.
      operationId: walletPaymentCallback
      tags: [Wallet Management]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [status, reference, payment_id, metadata]
              properties:
                status:
                  type: string
                  enum: [success, failure]
                reference:
                  type: string
                payment_id:
                  type: string
                gateway_txn_id:
                  type: string
                  nullable: true
                metadata:
                  type: object
                  required: [customer_id, company_id]
                  properties:
                    customer_id:
                      type: integer
                    company_id:
                      type: integer
      responses:
        '200':
          description: Callback processed
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/WalletDetails'

# ============================================================================
# COMPLETE JWT-PROTECTED CUSTOMER SERVICE API SPECIFICATION
# ============================================================================
#
# This OpenAPI specification provides COMPLETE coverage of all JWT-authenticated
# endpoints in the customer-service-v12 microservice for OneFoodDialer 2025.
#
# AUTHENTICATION:
# - All 19 endpoints require JWT authentication via jwt.auth middleware
# - JWT tokens are obtained from auth-service-v12
# - Tokens contain user info, roles, and company/tenant data
#
# COMPLETE ENDPOINT COVERAGE (19 endpoints):
#
# 1. Customer Management (1 endpoint):
#    - POST /v2/customers/find-or-create-keycloak
#
# 2. Customer Address Management (8 endpoints):
#    - GET    /v2/customer-address (list addresses)
#    - GET    /v2/customer-address/{id} (get specific address)
#    - GET    /v2/customer-address/{id}/set-default (set default - existing)
#    - GET    /v2/customer-address/selected (get selected address)
#    - POST   /v2/customer-addresses (create address) [ADDED]
#    - PUT    /v2/customer-addresses/{id} (update address) [ADDED]
#    - DELETE /v2/customer-addresses/{id} (delete address) [ADDED]
#    - PATCH  /v2/customer-addresses/{id}/set-default (set default) [ADDED]
#
# 3. Product Menu (1 endpoint):
#    - GET /v2/product-menu
#
# 4. Delivery Locations (2 endpoints):
#    - GET /v2/delivery-locations (list locations)
#    - GET /v2/delivery-locations/{id} (get specific location)
#
# 5. Wallet Management (7 endpoints):
#    - GET  /v2/customers/{id}/wallet (get wallet details)
#    - GET  /v2/customers/{id}/wallet/balance (get balance)
#    - POST /v2/customers/{id}/wallet/deposit (deposit funds)
#    - POST /v2/customers/{id}/wallet/withdraw (withdraw funds)
#    - GET  /v2/customers/{id}/wallet/transactions (get transactions)
#    - POST /v2/wallet/add (add funds) [ADDED]
#    - POST /v2/wallet/deduct (deduct funds) [ADDED]
#    - POST /v2/wallet/transfer (transfer funds) [ADDED]
#
# MIDDLEWARE CONFIGURATION:
# - Registered in bootstrap/app.php as 'jwt.auth' => KeycloakJwtAuth::class
# - Validates Keycloak JWT tokens
# - Extracts user information and roles
# - Provides request enhancement with auth data
#
# DATABASE:
# - Database: live_quickserve_8163
# - Company ID: 8163 (auto-filtered)
# - All operations scoped by company_id
#
# VALIDATION STATUS:
# - OpenAPI 3.0.3 compliant specification
# - All request/response schemas properly defined
# - Error responses follow consistent format
# - Authentication requirements clearly specified
# - Ready for import into any OpenAPI-compatible tool
#
# TESTING READY:
# - Import into Postman, Insomnia, or Swagger UI
# - All endpoints include example requests and responses
# - Proper HTTP status codes for all scenarios
# - Complete JWT authentication documentation
#
# Last Updated: January 2025
# Version: 2.1.0
# Status: Production Ready - Complete Coverage
# Total Endpoints: 19 JWT-protected endpoints
# ============================================================================
