<?php

namespace App\Services;

use App\Models\PaymentTransaction;
use App\Services\Payment\Gateway\PaymentGatewayInterface;
use App\Exceptions\Payment\PaymentException;
use App\Events\Payment\PaymentCompleted;
use App\Events\Payment\PaymentFailed;
use App\Events\Payment\PaymentInitiated;
use App\Events\Payment\PaymentRefunded;
use App\Events\Payment\WalletPaymentProcessed;
use App\Events\Payment\WalletPaymentFailed;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    /**
     * The registered payment gateways.
     *
     * @var array<string, PaymentGatewayInterface>
     */
    protected $gateways = [];

    /**
     * The metrics service instance.
     *
     * @var MetricsService
     */
    protected MetricsService $metricsService;

    /**
     * Create a new PaymentService instance.
     *
     * @param MetricsService $metricsService
     */
    public function __construct(MetricsService $metricsService)
    {
        $this->metricsService = $metricsService;
    }

    /**
     * Register a payment gateway.
     *
     * @param string $name
     * @param PaymentGatewayInterface $gateway
     * @return void
     */
    public function registerGateway(string $name, PaymentGatewayInterface $gateway): void
    {
        $this->gateways[$name] = $gateway;
    }

    /**
     * Get a payment gateway.
     *
     * @param string $name
     * @return PaymentGatewayInterface
     * @throws PaymentException
     */
    public function getGateway(string $name): PaymentGatewayInterface
    {
        if (!isset($this->gateways[$name])) {
            throw new PaymentException("Payment gateway '{$name}' not found");
        }

        return $this->gateways[$name];
    }

    /**
     * Get a transaction by ID.
     *
     * @param string $transactionId
     * @return PaymentTransaction
     * @throws PaymentException
     */
    public function getTransaction(string $transactionId): PaymentTransaction
    {
        try {
            // Extract the numeric ID from transaction ID (e.g., TXN123 -> 123)
            if (str_starts_with($transactionId, 'TXN')) {
                $numericId = substr($transactionId, 3);
                $transaction = PaymentTransaction::where('pk_transaction_id', $numericId)->firstOrFail();
            } else {
                // Fallback: try to find by pk_transaction_id directly
                $transaction = PaymentTransaction::findOrFail($transactionId);
            }
            return $transaction;
        } catch (\Exception $e) {
            throw new PaymentException("Transaction with ID {$transactionId} not found");
        }
    }

    /**
     * Initiate a payment.
     *
     * @param array $data
     * @return PaymentTransaction
     * @throws PaymentException
     */
    public function initiatePayment(array $data): PaymentTransaction
    {
        try {
            DB::beginTransaction();

            // Check for duplicate transaction with same order_id and customer_id
            if (isset($data['order_id']) && isset($data['customer_id'])) {
                $existingTransaction = PaymentTransaction::where('pre_order_id', $data['order_id'])
                    ->where('customer_id', $data['customer_id'])
                    ->where('status', '!=', 'failed') // Allow retry for failed transactions
                    ->first();

                if ($existingTransaction) {
                    DB::rollBack();

                    Log::warning('Duplicate transaction attempt prevented', [
                        'existing_transaction_id' => $existingTransaction->transaction_id,
                        'order_id' => $data['order_id'],
                        'customer_id' => $data['customer_id'],
                        'existing_status' => $existingTransaction->status
                    ]);

                    // Return existing transaction instead of creating duplicate
                    return $existingTransaction;
                }
            }

            // Apply transaction charges if enabled
            $amount = $data['amount'];
            $transactionCharges = 0;

            if (config('payment.transaction_charges.apply', false)) {
                $percentage = config('payment.transaction_charges.percentage', 0);
                $transactionCharges = round(($amount * $percentage) / 100, 2);
                $amount = round($amount + $transactionCharges, 2);
            }

            // Quick check for existing transaction (optimized for speed)
            $existingTransaction = PaymentTransaction::where('pre_order_id', $data['order_id'] ?? null)
                ->where('status', 'initiated')
                ->select('pk_transaction_id', 'pre_order_id', 'status', 'company_id', 'unit_id', 'customer_email', 'customer_phone', 'customer_name', 'wallet_amount', 'success_url', 'failure_url', 'context', 'recurring', 'discount')
                ->first();

            Log::info('Checking for existing payment transaction', [
                'order_id' => $data['order_id'] ?? null,
                'existing_found' => $existingTransaction ? true : false,
                'existing_id' => $existingTransaction ? $existingTransaction->pk_transaction_id : null
            ]);

            if ($existingTransaction) {
                // Quick update of existing transaction (optimized)
                $updateData = [
                    'payment_amount' => $amount,
                    'transaction_charges' => $transactionCharges,
                    'referer' => 'quickserve_order_api',
                    'modified_date' => now(),
                ];

                // Only update fields that are provided and different
                if (isset($data['customer_email']) && $data['customer_email'] !== $existingTransaction->customer_email) {
                    $updateData['customer_email'] = $data['customer_email'];
                }
                if (isset($data['customer_phone']) && $data['customer_phone'] !== $existingTransaction->customer_phone) {
                    $updateData['customer_phone'] = $data['customer_phone'];
                }
                if (isset($data['customer_name']) && $data['customer_name'] !== $existingTransaction->customer_name) {
                    $updateData['customer_name'] = $data['customer_name'];
                }

                $existingTransaction->update($updateData);
                $transaction = $existingTransaction;

                Log::info('Updated existing payment transaction', [
                    'pk_transaction_id' => $transaction->pk_transaction_id,
                    'pre_order_id' => $data['order_id'] ?? null,
                    'amount' => $amount
                ]);
            } else {
                // Create new transaction record
                $transaction = PaymentTransaction::create([
                    'company_id' => $data['company_id'] ?? 1, // Default company ID
                    'unit_id' => $data['unit_id'] ?? 1, // Default unit ID
                    'customer_id' => $data['customer_id'],
                    'customer_email' => $data['customer_email'] ?? null,
                    'customer_phone' => $data['customer_phone'] ?? null,
                    'customer_name' => $data['customer_name'] ?? null,
                    'payment_amount' => $amount,
                    'transaction_charges' => $transactionCharges,
                    'wallet_amount' => $data['wallet_amount'] ?? null,
                    'pre_order_id' => $data['order_id'] ?? null,
                    'status' => 'initiated',
                    'referer' => $data['referer'] ?? 'website',
                    'success_url' => $data['success_url'],
                    'failure_url' => $data['failure_url'],
                    'context' => $data['context'] ?? 'order',
                    'recurring' => $data['recurring'] ?? 0,
                    'discount' => $data['discount'] ?? 0,
                    'created_date' => now(),
                ]);

                Log::info('Created new payment transaction', [
                    'transaction_id' => $transaction->transaction_id,
                    'pre_order_id' => $data['order_id'] ?? null,
                    'amount' => $amount
                ]);
            }

            DB::commit();

            // Dispatch event
            event(new PaymentInitiated($transaction));

            return $transaction;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment initiation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw new PaymentException('Payment initiation failed: ' . $e->getMessage());
        }
    }

    /**
     * Process a payment.
     *
     * @param string $transactionId
     * @param string $gateway
     * @return array
     * @throws PaymentException
     */
    public function processPayment(string $transactionId, string $gateway): array
    {
        try {
            $transaction = $this->getTransaction($transactionId);

            // Capture authorization token from current request for callback authentication
            $authorizationToken = $this->extractAuthorizationTokenFromRequest($transactionId);

            // Update gateway
            $transaction->gateway = $gateway;
            $transaction->save();

            // Store authorization token in cache for callback use (not in DB)
            if ($authorizationToken) {
                $cacheKey = 'payment_auth_token_' . $transactionId;
                // Store for 30 minutes (optimized for file cache - shorter TTL for faster cleanup)
                // Payment callbacks usually happen within 2-5 minutes
                cache()->put($cacheKey, $authorizationToken, 1800);

                Log::info('Authorization token stored in file cache for callback', [
                    'transaction_id' => $transactionId,
                    'cache_key' => $cacheKey,
                    'cache_store' => config('cache.default'),
                    'ttl_seconds' => 1800,
                    'has_auth_token' => true
                ]);
            }

            // Get gateway adapter
            $gatewayAdapter = $this->getGateway($gateway);

            // Transform transaction data for gateway compatibility
            $gatewayData = [
                'transaction_id' => $transaction->transaction_id,
                'order_id' => $transaction->order_id, // Uses the accessor method
                'amount' => $transaction->amount, // Uses the accessor method
                'customer_id' => $transaction->customer_id,
                'customer_name' => $transaction->customer_name,
                'customer_email' => $transaction->customer_email,
                'customer_phone' => $transaction->customer_phone,
                'description' => $transaction->description ?? 'Payment for Order #' . $transaction->order_id,
                'callback_url' => url('/api/v2/payments/callback'),
                'success_url' => $transaction->success_url,
                'failure_url' => $transaction->failure_url
            ];

            // Get form data
            $formData = $gatewayAdapter->generatePaymentForm($gatewayData);

            // Update transaction with gateway details
            $transaction->gateway = $gateway;
            if (isset($formData['order_id'])) {
                $transaction->gateway_transaction_id = $formData['order_id'];
            }
            $transaction->save();

            return $formData;
        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'gateway' => $gateway
            ]);

            throw new PaymentException('Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle a payment callback.
     *
     * @param array $data
     * @param \Illuminate\Http\Request|null $request
     * @return PaymentTransaction
     * @throws PaymentException
     */
    public function handleCallback(array $data, $request = null): PaymentTransaction
    {
        try {
            // Log the callback request
            // app(PaymentLogService::class)->logEvent(
            //     'callback',
            //     'received',
            //     [],
            //     $data,
            //     null,
            //     null,
            //     $request
            // );
            Log::info('Payment callback received', [
                'callback',
                'received',
                [],
                $data,
                null,
                null,
                $request
            ]);

            // Identify gateway from response data
            $gateway = $this->identifyGateway($data);

            // Get transaction ID from response data
            $transactionId = $this->extractTransactionId($data, $gateway);

            // Get transaction
            $transaction = $this->getTransaction($transactionId);

            // Get gateway adapter
            $gatewayAdapter = $this->getGateway($gateway);

            // Verify payment
            $result = $gatewayAdapter->verifyPayment($transactionId, $data);

            // Update transaction status
            $transaction->status = $result['status'];
            $transaction->gateway_transaction_id = $result['gateway_transaction_id'] ?? null;
            // Note: metadata field doesn't exist in current table schema
            // $transaction->metadata = $result['metadata'] ?? null;
            $transaction->save();

            // Log the verification result
            // app(PaymentLogService::class)->logEvent(
            //     'verify',
            //     $result['success'] ? 'success' : 'failure',
            //     $data,
            //     $result,
            //     $transaction->transaction_id,
            //     $gateway,
            //     $request
            // );

            Log::info('Payment verification result logged', [
                'verify',
                $result['success'] ? 'success' : 'failure',
                $data,
                $result,
                $transaction->transaction_id,
                $gateway,
                $request
            ]);

            // Dispatch event and update related tables
            if ($result['status'] === 'completed') {
                event(new PaymentCompleted($transaction));

                // Update all related tables
                $this->updateRelatedTablesOnPaymentSuccess($transaction);

                // Process wallet payment if applicable
                if ($transaction->wallet_amount > 0) {
                    $this->processWalletPayment($transaction);
                }
            } elseif ($result['status'] === 'failed') {
                event(new PaymentFailed($transaction));

                // Update related tables for failed payment
                $this->updateRelatedTablesOnPaymentFailure($transaction);

                // Call external failure URL if provided
                if ($transaction->failure_url) {
                    $this->callExternalFailureUrl($transaction);
                }
            }

            return $transaction;
        } catch (\Exception $e) {
            Log::error('Payment callback handling failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            // Log the error
            // app(PaymentLogService::class)->logEvent(
            //     'callback',
            //     'error',
            //     $data,
            //     ['error' => $e->getMessage()],
            //     null,
            //     null,
            //     $request
            // );

            Log::error('Payment callback error', [
                'callback',
                'error',
                $data,
                ['error' => $e->getMessage()],
                null,
                null,
                $request
            ]);

            throw new PaymentException('Payment callback handling failed: ' . $e->getMessage());
        }
    }

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     * @throws PaymentException
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            $transaction = $this->getTransaction($transactionId);

            if ($transaction->status !== 'completed') {
                throw new PaymentException('Cannot refund a transaction that is not completed');
            }

            // Get gateway adapter
            $gatewayAdapter = $this->getGateway($transaction->gateway);

            // Process refund
            $result = $gatewayAdapter->refundPayment($transaction->gateway_transaction_id, $amount);

            // Update transaction status
            if ($result['success']) {
                $transaction->status = 'refunded';
                $transaction->save();

                // Dispatch event - Note: PaymentRefunded now expects Payment model
                // For now, we'll skip the event to avoid type mismatch
                // TODO: Align event models or create separate events for PaymentTransaction
                // event(new PaymentRefunded($transaction, $amount));
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);

            throw new PaymentException('Payment refund failed: ' . $e->getMessage());
        }
    }

    /**
     * Identify the payment gateway from response data.
     *
     * @param array $data
     * @return string
     * @throws PaymentException
     */
    protected function identifyGateway(array $data): string
    {
        // Logic to identify gateway from response data
        if (isset($data['payuMoneyId'])) {
            return 'payu';
        } elseif (isset($data['payment_request_id'])) {
            return 'instamojo';
        } elseif (isset($data['CHECKSUMHASH']) && isset($data['RESPCODE'])) {
            return 'paytm';
        } elseif (isset($data['Transaction_Tag']) && isset($data['transaction_key'])) {
            return 'payeezy';
        } elseif (isset($data['checksum']) && isset($data['orderid']) && isset($data['statuscode'])) {
            return 'mobikwik';
        } elseif (isset($data['PayerID']) || isset($data['token'])) {
            return 'paypal';
        } elseif (isset($data['payment_intent'])) {
            return 'stripe';
        } elseif (isset($data['razorpay_payment_id']) && isset($data['razorpay_order_id'])) {
            return 'razorpay';
        } elseif (isset($data['transaction_id']) && isset($data['payment_status'])) {
            // Test gateway pattern - for testing purposes
            return 'test_gateway';
        }

        throw new PaymentException('Unknown payment gateway');
    }

    /**
     * Extract the transaction ID from response data.
     *
     * @param array $data
     * @param string $gateway
     * @return string
     * @throws PaymentException
     */
    protected function extractTransactionId(array $data, string $gateway): string
    {
        // Logic to extract transaction ID from response data based on gateway
        switch ($gateway) {
            case 'payu':
                return $data['udf1'] ?? '';
            case 'instamojo':
                return $data['transaction_id'] ?? '';
            case 'paytm':
                return $data['ORDERID'] ?? '';
            case 'payeezy':
                return $data['transaction_id'] ?? '';
            case 'mobikwik':
                return $data['orderid'] ?? '';
            case 'paypal':
                return $data['transaction_id'] ?? '';
            case 'stripe':
                return $data['transaction_id'] ?? '';
            case 'razorpay':
                // For Razorpay, check if transaction_id is provided directly (from mobile app)
                if (isset($data['transaction_id'])) {
                    return $data['transaction_id'];
                }
                // Alternative: find transaction by Razorpay order_id
                if (isset($data['razorpay_order_id'])) {
                    $transaction = PaymentTransaction::where('gateway_transaction_id', $data['razorpay_order_id'])->first();
                    if ($transaction) {
                        return $transaction->transaction_id;
                    }
                }
                throw new PaymentException('Cannot extract transaction ID for Razorpay payment');
            case 'test_gateway':
                return $data['transaction_id'] ?? '';
            default:
                throw new PaymentException('Cannot extract transaction ID for unknown gateway');
        }
    }

    /**
     * Process wallet payment for a transaction with atomic operations.
     *
     * @param PaymentTransaction $transaction
     * @return void
     * @throws PaymentException
     */
    protected function processWalletPayment(PaymentTransaction $transaction): void
    {
        // Only use transactions in non-testing environment
        $useTransactions = !app()->environment('testing');
        $startTime = microtime(true);

        try {
            if ($useTransactions) {
                DB::beginTransaction();
            }

            // Log wallet payment processing
            // app(PaymentLogService::class)->logEvent(
            //     'wallet_payment',
            //     'processing',
            //     [],
            //     [
            //         'transaction_id' => $transaction->transaction_id,
            //         'wallet_amount' => $transaction->wallet_amount,
            //         'customer_id' => $transaction->customer_id,
            //     ],
            //     $transaction->transaction_id,
            //     'wallet'
            // );

            Log::info('Processing wallet payment', [
                'wallet_payment',
                'processing',
                [],
                [
                    'transaction_id' => $transaction->transaction_id,
                    'wallet_amount' => $transaction->wallet_amount,
                    'customer_id' => $transaction->customer_id,
                ],
                $transaction->transaction_id,
                'wallet'
            ]);

            // Validate wallet balance before processing
            $walletBalance = $this->getWalletBalance($transaction->customer_id);
            if ($walletBalance < $transaction->wallet_amount) {
                throw new PaymentException('Insufficient wallet balance');
            }

            // Call customer service to deduct wallet amount
            $walletResponse = Http::timeout(30)
                ->retry(3, 1000)
                ->withHeaders([
                    'X-Service' => 'payment-service-v12',
                    'X-Request-ID' => uniqid('wallet_'),
                    'X-Correlation-ID' => $transaction->transaction_id,
                ])
                ->post(config('services.customer.url') . '/api/v2/wallet/deduct', [
                    'customer_id' => $transaction->customer_id,
                    'amount' => $transaction->wallet_amount,
                    'description' => "Payment for order #{$transaction->order_id}",
                    'transaction_id' => $transaction->transaction_id,
                    'metadata' => [
                        'payment_transaction_id' => $transaction->transaction_id,
                        'order_id' => $transaction->order_id,
                        'source' => 'payment_service',
                    ]
                ]);

            if (!$walletResponse->successful()) {
                throw new PaymentException('Wallet deduction failed: ' . $walletResponse->body());
            }

            $walletData = $walletResponse->json();

            // Update transaction with wallet details
            $transaction->update([
                'wallet_transaction_id' => $walletData['data']['transaction_id'] ?? null,
                'wallet_balance_after' => $walletData['data']['balance_after'] ?? null,
            ]);

            if ($useTransactions) {
                DB::commit();
            }

            // Publish wallet payment event
            event(new WalletPaymentProcessed($transaction, $walletData));

            // Record metrics
            $duration = microtime(true) - $startTime;
            $this->metricsService->recordWalletOperation('deduct', $duration, 'success');

            // Log success
            // app(PaymentLogService::class)->logEvent(
            //     'wallet_payment',
            //     'success',
            //     [],
            //     [
            //         'transaction_id' => $transaction->transaction_id,
            //         'wallet_amount' => $transaction->wallet_amount,
            //         'wallet_transaction_id' => $walletData['data']['transaction_id'] ?? null,
            //         'balance_after' => $walletData['data']['balance_after'] ?? null,
            //         'message' => 'Wallet payment processed successfully',
            //     ],
            //     $transaction->transaction_id,
            //     'wallet'
            // );

            Log::info('Wallet payment processed successfully', [
                'wallet_payment',
                'success',
                [],
                [
                    'transaction_id' => $transaction->transaction_id,
                    'wallet_amount' => $transaction->wallet_amount,
                    'wallet_transaction_id' => $walletData['data']['transaction_id'] ?? null,
                    'balance_after' => $walletData['data']['balance_after'] ?? null,
                    'message' => 'Wallet payment processed successfully',
                ],
                $transaction->transaction_id,
                'wallet'
            ]);

        } catch (\Exception $e) {
            if ($useTransactions) {
                DB::rollBack();
            }

            Log::error('Wallet payment processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction->transaction_id,
                'wallet_amount' => $transaction->wallet_amount,
                'customer_id' => $transaction->customer_id,
                'trace' => $e->getTraceAsString()
            ]);

            // Log the error
            // app(PaymentLogService::class)->logEvent(
            //     'wallet_payment',
            //     'error',
            //     [],
            //     [
            //         'error' => $e->getMessage(),
            //         'transaction_id' => $transaction->transaction_id,
            //         'wallet_amount' => $transaction->wallet_amount,
            //         'customer_id' => $transaction->customer_id,
            //     ],
            //     $transaction->transaction_id,
            //     'wallet'
            // );

            Log::error('Wallet payment error', [
                'wallet_payment',
                'error',
                [],
                [
                    'error' => $e->getMessage(),
                    'transaction_id' => $transaction->transaction_id,
                    'wallet_amount' => $transaction->wallet_amount,
                    'customer_id' => $transaction->customer_id,
                ],
                $transaction->transaction_id,
                'wallet'
            ]);

            // Record error metrics
            $duration = microtime(true) - $startTime;
            $this->metricsService->recordWalletOperation('deduct', $duration, 'failed');
            $this->metricsService->recordError('payment-service', 'wallet_payment', get_class($e));

            // Publish wallet payment failed event
            event(new WalletPaymentFailed($transaction, $e->getMessage()));

            throw new PaymentException('Wallet payment failed: ' . $e->getMessage());
        }
    }

    /**
     * Get wallet balance for a customer.
     *
     * @param int $customerId
     * @return float
     * @throws PaymentException
     */
    protected function getWalletBalance(int $customerId): float
    {
        try {
            $response = Http::timeout(10)
                ->retry(2, 500)
                ->withHeaders([
                    'X-Service' => 'payment-service-v12',
                    'X-Request-ID' => uniqid('balance_'),
                ])
                ->get(config('services.customer.url') . "/api/v2/wallet/{$customerId}/balance");

            if (!$response->successful()) {
                throw new PaymentException('Failed to fetch wallet balance: ' . $response->body());
            }

            $data = $response->json();
            return (float) ($data['data']['balance'] ?? 0);
        } catch (\Exception $e) {
            Log::error('Failed to get wallet balance', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
            throw new PaymentException('Failed to get wallet balance: ' . $e->getMessage());
        }
    }

    /**
     * Get transaction statistics.
     *
     * @param array $filters
     * @return array
     */
    public function getTransactionStatistics(array $filters = []): array
    {
        $query = PaymentTransaction::query();

        // Apply filters
        if (isset($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        if (isset($filters['gateway'])) {
            $query->where('gateway', $filters['gateway']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Get total transactions
        $totalTransactions = $query->count();

        // Get total amount
        $totalAmount = $query->sum('amount');

        // Get transactions by status
        $transactionsByStatus = $query->select('status', DB::raw('count(*) as count'), DB::raw('sum(amount) as amount'))
            ->groupBy('status')
            ->get()
            ->keyBy('status')
            ->toArray();

        // Get transactions by gateway
        $transactionsByGateway = $query->select('gateway', DB::raw('count(*) as count'), DB::raw('sum(amount) as amount'))
            ->groupBy('gateway')
            ->get()
            ->keyBy('gateway')
            ->toArray();

        // Get recent transactions
        $recentTransactions = $query->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return [
            'total_transactions' => $totalTransactions,
            'total_amount' => $totalAmount,
            'transactions_by_status' => $transactionsByStatus,
            'transactions_by_gateway' => $transactionsByGateway,
            'recent_transactions' => $recentTransactions,
        ];
    }

    /**
     * Update all related tables when payment is successful
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    protected function updateRelatedTablesOnPaymentSuccess(PaymentTransaction $transaction): void
    {
        try {
            DB::beginTransaction();

            Log::info('Updating related tables for successful payment', [
                'transaction_id' => $transaction->transaction_id,
                'pre_order_id' => $transaction->pre_order_id,
                'customer_id' => $transaction->customer_id,
                'amount' => $transaction->payment_amount
            ]);

            // 1. Update ORDERS table - mark as paid
            $this->updateOrdersTable($transaction);

            // 2. Create/Update INVOICE if needed
            $invoiceId = $this->createOrUpdateInvoice($transaction);

            // 3. Create INVOICE_PAYMENTS record
            if ($invoiceId) {
                $this->createInvoicePayment($transaction, $invoiceId);
            }

            // 4. Update CUSTOMER_WALLET if wallet amount was used
            if ($transaction->wallet_amount > 0) {
                $this->updateCustomerWalletForPayment($transaction);
            }

            // 5. Call external success URL if provided (for service integration)
            if ($transaction->success_url) {
                $this->callExternalSuccessUrl($transaction);
            }

            DB::commit();

            Log::info('Successfully updated all related tables for payment', [
                'transaction_id' => $transaction->transaction_id,
                'invoice_id' => $invoiceId
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update related tables for payment', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new PaymentException('Failed to update related payment tables: ' . $e->getMessage());
        }
    }

    /**
     * Call external success URL for service integration
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    protected function callExternalSuccessUrl(PaymentTransaction $transaction): void
    {
        try {
            if (!$transaction->success_url) {
                return;
            }

            Log::info('Calling external success URL', [
                'transaction_id' => $transaction->transaction_id,
                'success_url' => $transaction->success_url,
                'gateway' => $transaction->gateway,
                'gateway_transaction_id' => $transaction->gateway_transaction_id
            ]);

            $callbackData = [
                'payment_service_transaction_id' => $transaction->gateway_transaction_id,
                'gateway' => $transaction->gateway,
                'amount' => $transaction->payment_amount,
                'status' => 'completed',
                'transaction_id' => $transaction->transaction_id,
                'customer_id' => $transaction->customer_id,
                'order_id' => $transaction->pre_order_id
            ];

            Log::info('External success URL call data', [
                'transaction_id' => $transaction->transaction_id,
                'success_url' => $transaction->success_url,
                'callback_data' => $callbackData
            ]);

            // $response = Http::timeout(60) // Increased timeout to 60 seconds for order creation
            //     ->connectTimeout(10) // Connection timeout
            //     ->retry(3, 1000) // Increased retries with longer delay
            // Prepare headers for callback request
            $headers = [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Service' => 'payment-service-v12',
                'X-Request-ID' => uniqid('callback_'),
                'X-Correlation-ID' => $transaction->transaction_id,
            ];

            // Add authorization token if available (for protected APIs)
            $cacheKey = 'payment_auth_token_' . $transaction->transaction_id;
            $authorizationToken = cache()->get($cacheKey);

            if ($authorizationToken) {
                $headers['Authorization'] = $authorizationToken;
                Log::info('Adding authorization token to callback request', [
                    'transaction_id' => $transaction->transaction_id,
                    'cache_key' => $cacheKey,
                    'has_token' => true
                ]);

                // Clean up the token from cache after use
                cache()->forget($cacheKey);
            } else {
                Log::warning('No authorization token available for callback', [
                    'transaction_id' => $transaction->transaction_id,
                    'cache_key' => $cacheKey,
                    'success_url' => $transaction->success_url
                ]);
            }

            $response = Http::timeout(3) // Connection timeout
                ->retry(1, 200) // Increased retries with longer delay
                ->withHeaders($headers)
                ->post($transaction->success_url, $callbackData);
            

            if ($response->successful()) {
                Log::info('External success URL called successfully', [
                    'transaction_id' => $transaction->transaction_id,
                    'success_url' => $transaction->success_url,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            } else {
                Log::warning('External success URL call failed', [
                    'transaction_id' => $transaction->transaction_id,
                    'success_url' => $transaction->success_url,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to call external success URL', [
                'transaction_id' => $transaction->transaction_id,
                'success_url' => $transaction->success_url,
                'error' => $e->getMessage()
            ]);

            // Don't throw exception - this is a callback, not critical for payment completion
        }
    }

    /**
     * Update related tables when payment fails
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    protected function updateRelatedTablesOnPaymentFailure(PaymentTransaction $transaction): void
    {
        try {
            Log::info('Updating related tables for failed payment', [
                'transaction_id' => $transaction->transaction_id,
                'pre_order_id' => $transaction->pre_order_id
            ]);

            // Update orders table to mark payment as failed
            if ($transaction->pre_order_id) {
                DB::table('orders')
                    ->where('order_no', $transaction->pre_order_id)
                    ->update([
                        'amount_paid' => 0,
                        'payment_mode' => 'failed',
                        'last_modified' => now()
                    ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to update related tables for failed payment', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update ORDERS table to mark payment as completed
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    protected function updateOrdersTable(PaymentTransaction $transaction): void
    {
        if (!$transaction->pre_order_id) {
            Log::warning('No pre_order_id found for transaction', [
                'transaction_id' => $transaction->transaction_id
            ]);
            return;
        }

        // Update orders where order_no matches pre_order_id
        $updatedRows = DB::table('orders')
            ->where('order_no', $transaction->pre_order_id)
            ->update([
                'amount_paid' => 1,
                'payment_mode' => $transaction->gateway ?? 'online',
                'last_modified' => now()
            ]);

        Log::info('Updated orders table', [
            'transaction_id' => $transaction->transaction_id,
            'order_no' => $transaction->pre_order_id,
            'updated_rows' => $updatedRows
        ]);
    }

    /**
     * Create or update invoice for the payment
     *
     * @param PaymentTransaction $transaction
     * @return int|null Invoice ID
     */
    protected function createOrUpdateInvoice(PaymentTransaction $transaction): ?int
    {
        if (!$transaction->customer_id || !$transaction->pre_order_id) {
            return null;
        }

        // Check if invoice already exists for this customer and order
        $existingInvoice = DB::table('invoice')
            ->where('cust_ref_id', $transaction->customer_id)
            ->where('order_bill_no', $transaction->pre_order_id)
            ->first();

        if ($existingInvoice) {
            Log::info('Invoice already exists', [
                'invoice_id' => $existingInvoice->invoice_id,
                'transaction_id' => $transaction->transaction_id
            ]);
            return $existingInvoice->invoice_id;
        }

        // Create new invoice
        $invoiceNo = 'INV-' . $transaction->customer_id . '-' . date('ymd') . '-' . time();

        $invoiceId = DB::table('invoice')->insertGetId([
            'company_id' => $transaction->company_id ?? 1,
            'unit_id' => $transaction->unit_id ?? 1,
            'invoice_no' => $invoiceNo,
            'fk_kitchen_code' => 0,
            'date' => now()->toDateString(),
            'cust_ref_id' => $transaction->customer_id,
            'cust_name' => $transaction->customer_name,
            'order_bill_no' => $transaction->pre_order_id,
            'status' => 1 // Paid
        ]);

        Log::info('Created new invoice', [
            'invoice_id' => $invoiceId,
            'invoice_no' => $invoiceNo,
            'transaction_id' => $transaction->transaction_id
        ]);

        return $invoiceId;
    }

    /**
     * Create invoice payment record
     *
     * @param PaymentTransaction $transaction
     * @param int $invoiceId
     * @return void
     */
    protected function createInvoicePayment(PaymentTransaction $transaction, int $invoiceId): void
    {
        $invoicePaymentId = DB::table('invoice_payments')->insertGetId([
            'company_id' => $transaction->company_id ?? 1,
            'unit_id' => $transaction->unit_id ?? 1,
            'invoice_ref_id' => $invoiceId,
            'actual_invoice_amount' => $transaction->payment_amount,
            'invoice_amount' => $transaction->payment_amount,
            'discounted_amount' => $transaction->discount ?? 0,
            'tax' => 0, // Calculate if needed
            'delivery_charges' => 0,
            'service_charges' => 0,
            'amount_paid' => $transaction->payment_amount,
            'amount_due' => 0,
            'mode_of_payment' => $transaction->gateway ?? 'online',
            'date' => now()->toDateString(),
            'current_amount_paid' => $transaction->payment_amount
        ]);

        Log::info('Created invoice payment record', [
            'invoice_payment_id' => $invoicePaymentId,
            'invoice_id' => $invoiceId,
            'transaction_id' => $transaction->transaction_id,
            'amount' => $transaction->payment_amount
        ]);
    }

    /**
     * Update customer wallet for payment transaction
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    protected function updateCustomerWalletForPayment(PaymentTransaction $transaction): void
    {
        if ($transaction->wallet_amount <= 0) {
            return;
        }

        $walletId = DB::table('customer_wallet')->insertGetId([
            'company_id' => $transaction->company_id ?? 1,
            'unit_id' => $transaction->unit_id ?? 1,
            'fk_customer_code' => $transaction->customer_id,
            'wallet_amount' => $transaction->wallet_amount,
            'amount_type' => 'dr', // Debit from wallet
            'reference_no' => $transaction->gateway_transaction_id,
            'payment_date' => now()->toDateString(),
            'description' => "Rs. {$transaction->wallet_amount} deducted for payment via {$transaction->gateway} - Transaction: {$transaction->transaction_id}",
            'created_by' => $transaction->customer_id,
            'updated_by' => $transaction->customer_id,
            'context' => 'customer',
            'payment_type' => 'wallet'
        ]);

        Log::info('Updated customer wallet for payment', [
            'wallet_id' => $walletId,
            'transaction_id' => $transaction->transaction_id,
            'customer_id' => $transaction->customer_id,
            'wallet_amount' => $transaction->wallet_amount
        ]);
    }

    /**
     * Call external failure URL for service integration
     *
     * @param PaymentTransaction $transaction
     * @return void
     */
    protected function callExternalFailureUrl(PaymentTransaction $transaction): void
    {
        try {
            if (!$transaction->failure_url) {
                return;
            }

            Log::info('Calling external failure URL', [
                'transaction_id' => $transaction->transaction_id,
                'failure_url' => $transaction->failure_url,
                'gateway' => $transaction->gateway,
                'status' => $transaction->status
            ]);

            $callbackData = [
                'payment_service_transaction_id' => $transaction->gateway_transaction_id,
                'gateway' => $transaction->gateway,
                'amount' => $transaction->payment_amount,
                'status' => 'failed',
                'transaction_id' => $transaction->transaction_id,
                'customer_id' => $transaction->customer_id,
                'order_id' => $transaction->pre_order_id,
                'failure_reason' => 'Payment gateway failure'
            ];

            // $response = Http::timeout(60) // Increased timeout to 60 seconds for order processing
            //     ->connectTimeout(10) // Connection timeout
            //     ->retry(3, 1000) // Increased retries with longer delay
            // Prepare headers for failure callback request
            $headers = [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Service' => 'payment-service-v12',
                'X-Request-ID' => uniqid('callback_'),
                'X-Correlation-ID' => $transaction->transaction_id,
            ];

            // Add authorization token if available (for protected APIs)
            $cacheKey = 'payment_auth_token_' . $transaction->transaction_id;
            $authorizationToken = cache()->get($cacheKey);

            if ($authorizationToken) {
                $headers['Authorization'] = $authorizationToken;
                Log::info('Adding authorization token to failure callback request', [
                    'transaction_id' => $transaction->transaction_id,
                    'cache_key' => $cacheKey,
                    'has_token' => true
                ]);

                // Clean up the token from cache after use
                cache()->forget($cacheKey);
            }

            $response = Http::timeout(10) // Increased timeout to 60 seconds for order processing
                ->retry(2, 500) // Increased retries with longer delay
                ->withHeaders($headers)
                ->post($transaction->failure_url, $callbackData);

            if ($response->successful()) {
                Log::info('External failure URL called successfully', [
                    'transaction_id' => $transaction->transaction_id,
                    'failure_url' => $transaction->failure_url,
                    'response_status' => $response->status()
                ]);
            } else {
                Log::warning('External failure URL call failed', [
                    'transaction_id' => $transaction->transaction_id,
                    'failure_url' => $transaction->failure_url,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to call external failure URL', [
                'transaction_id' => $transaction->transaction_id,
                'failure_url' => $transaction->failure_url,
                'error' => $e->getMessage()
            ]);

            // Don't throw exception - this is a callback, not critical
        }
    }

    /**
     * Extract authorization token from request using multiple methods
     *
     * @param string $transactionId
     * @return string|null
     */
    private function extractAuthorizationTokenFromRequest(string $transactionId): ?string
    {
        $authorizationToken = null;
        $method = 'none';

        // Method 1: Try Authorization header directly
        $authHeader = request()->header('Authorization');
        if ($authHeader) {
            $authorizationToken = $authHeader;
            $method = 'authorization_header';
        }

        // Method 2: Try Laravel's bearerToken() helper
        if (!$authorizationToken) {
            $bearerToken = request()->bearerToken();
            if ($bearerToken) {
                $authorizationToken = 'Bearer ' . $bearerToken;
                $method = 'bearer_token_helper';
            }
        }

        // Method 3: Try different header variations
        if (!$authorizationToken) {
            $headerVariations = ['authorization', 'Authorization', 'AUTHORIZATION', 'HTTP_AUTHORIZATION'];
            foreach ($headerVariations as $headerName) {
                $headerValue = request()->header($headerName) ?? $_SERVER[$headerName] ?? null;
                if ($headerValue) {
                    $authorizationToken = $headerValue;
                    $method = 'header_variation_' . $headerName;
                    break;
                }
            }
        }

        // Method 4: Try from server variables (for some server configurations)
        if (!$authorizationToken) {
            $serverVars = ['HTTP_AUTHORIZATION', 'REDIRECT_HTTP_AUTHORIZATION'];
            foreach ($serverVars as $serverVar) {
                if (isset($_SERVER[$serverVar]) && $_SERVER[$serverVar]) {
                    $authorizationToken = $_SERVER[$serverVar];
                    $method = 'server_var_' . $serverVar;
                    break;
                }
            }
        }

        // Normalize token format (ensure it starts with "Bearer ")
        if ($authorizationToken) {
            $authorizationToken = trim($authorizationToken);

            // If token doesn't start with "Bearer ", add it
            if (!str_starts_with($authorizationToken, 'Bearer ')) {
                // Check if it's just the token without "Bearer " prefix
                if (strlen($authorizationToken) > 10 && !str_contains($authorizationToken, ' ')) {
                    $authorizationToken = 'Bearer ' . $authorizationToken;
                }
            }
        }

        // Log the result for debugging
        if ($authorizationToken) {
            Log::info('Authorization token captured successfully', [
                'transaction_id' => $transactionId,
                'method' => $method,
                'token_length' => strlen($authorizationToken),
                'starts_with_bearer' => str_starts_with($authorizationToken, 'Bearer '),
                'token_preview' => substr($authorizationToken, 0, 20) . '...'
            ]);
        } else {
            Log::warning('No authorization token found in request', [
                'transaction_id' => $transactionId,
                'tried_methods' => [
                    'authorization_header' => request()->header('Authorization') ? 'found' : 'not_found',
                    'bearer_token_helper' => request()->bearerToken() ? 'found' : 'not_found',
                    'server_http_auth' => isset($_SERVER['HTTP_AUTHORIZATION']) ? 'found' : 'not_found',
                ],
                'all_headers' => request()->headers->all()
            ]);
        }

        return $authorizationToken;
    }
}
