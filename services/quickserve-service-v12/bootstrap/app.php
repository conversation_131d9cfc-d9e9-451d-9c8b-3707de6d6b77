<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\{KeycloakJwtAuth,ValidateCompanyId};

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Register JWT authentication middleware
        $middleware->alias([
            'keycloak.auth' => KeycloakJwtAuth::class,
            'validate.company' => ValidateCompanyId::class
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
