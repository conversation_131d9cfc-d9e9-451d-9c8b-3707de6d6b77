<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ValidateCompanyId
{
    /**
     * Validate presence of company_id: for GET -> query param, for other methods -> request body.
     */
    public function handle(Request $request, Closure $next)
    {
        $method = strtoupper($request->method());

        if ($method === 'GET') {
            $companyId = $request->query('company_id');
        } else {
            $companyId = $request->input('company_id');
        }

        if (empty($companyId) || !is_numeric($companyId)) {
            return response()->json([
                'success' => false,
                'message' => 'company_id is required and must be numeric',
            ], 400);
        }

        // Normalize to integer so controllers can rely on type
        $request->merge(['company_id' => (int) $companyId]);

        return $next($request);
    }
}
