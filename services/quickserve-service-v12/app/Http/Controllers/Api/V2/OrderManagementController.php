<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V2;

use App\Models\Order;
use App\Models\OrderDetail;
use App\Services\PaymentOrderUpdateService;
use App\Http\Requests\Api\V2\GetCustomerOrdersRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Schema;
use Exception;

/**
 * Controller for comprehensive order management
 */
class OrderManagementController extends BaseController
{
    public function __construct(
        protected PaymentOrderUpdateService $paymentOrderUpdateService
    ) {}

    /**
     * Create a new pre-order with temporary tables and initiate payment
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createOrder(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validated = $request->validate([
                'customer_id' => 'required|integer',
                'customer_address' => 'required|string|max:250',
                'location_code' => 'required|integer',
                'location_name' => 'required|string|max:45',
                'city' => 'required|integer',
                'city_name' => 'required|string|max:45',

                // Organization and kitchen context (must come from request)
                'company_id' => 'required|integer',
                'unit_id' => 'required|integer',
                'fk_kitchen_code' => 'required|integer',

                // Repeating model (backward compatible)
                'meals' => 'required_without:meals_by_date|array',
                'meals.*.product_code' => 'required_with:meals|integer',
                'meals.*.quantity' => 'required_with:meals|integer|min:1',

                // Date-based structure for repeating model
                'start_date' => 'required_without:meals_by_date|date_format:Y-m-d|after_or_equal:today',
                'selected_days' => 'required_without:meals_by_date|array|min:1|max:7', // Array of day numbers (0-6)
                'selected_days.*' => 'integer|min:0|max:6', // Each day must be 0-6 (Sunday-Saturday)
                'subscription_days' => 'required_without:meals_by_date|integer|min:1|max:300', // Number of delivery days

                // New per-date model (heterogeneous meals)
                'meals_by_date' => 'sometimes|array|min:1',
                'meals_by_date.*.date' => 'required_with:meals_by_date|date_format:Y-m-d|after_or_equal:today',
                'meals_by_date.*.meals' => 'required_with:meals_by_date|array|min:1',
                'meals_by_date.*.meals.*.product_code' => 'required_with:meals_by_date|integer',
                'meals_by_date.*.meals.*.quantity' => 'required_with:meals_by_date|integer|min:1',

                // Legacy support (optional)
                'days_preference' => 'nullable|string', // e.g., "1,2,3,4,5" - for backward compatibility

                'delivery_time' => 'nullable|string',
                'delivery_end_time' => 'nullable|string',
                'food_preference' => 'nullable|string|max:255',
                // Express flag
                'is_express' => 'sometimes|boolean',

                // Payment method
                'payment_method' => 'sometimes|string|in:online,wallet',
            ]);

            // Fetch customer details from database
            $customerDetails = $this->getCustomerDetails($validated['customer_id']);
            if (!$customerDetails) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Branch: per-date model vs repeating model
            $usingPerDate = isset($validated['meals_by_date']) && is_array($validated['meals_by_date']) && count($validated['meals_by_date']) > 0;

            // Normalize express flag
            $validated['is_express'] = (bool) ($request->boolean('is_express'));

            DB::beginTransaction();

            // Generate unique order number
            $orderNo = $this->generateOrderNumber();

            $tempPreOrderIds = [];

            // Determine message based on payment method
            $isWalletPayment = ($validated['payment_method'] ?? 'online') === 'wallet';
            $responseMessage = $isWalletPayment ? 'Order created successfully' : 'Pre-order created successfully';

            if ($usingPerDate) {
                // Per-date heterogeneous scheduling
                foreach ($validated['meals_by_date'] as $entry) {
                    $date = $entry['date'];
                    $processedMeals = $this->processMeals($entry['meals']);
                    if (empty($processedMeals)) {
                        DB::rollBack();
                        return response()->json([
                            'success' => false,
                            'message' => 'No valid meals found for date: ' . $date
                        ], 400);
                    }

                    // Enforce one item per meal type per day (breakfast/lunch)
                    $mealsByType = $this->groupMealsByType($processedMeals);
                    foreach (['breakfast', 'lunch'] as $restrictedType) {
                        if (!empty($mealsByType[$restrictedType])) {
                            $distinctProducts = array_unique(array_column($mealsByType[$restrictedType], 'product_code'));
                            $totalQty = array_sum(array_map(fn($m) => (int)($m['quantity'] ?? 0), $mealsByType[$restrictedType]));
                            if (count($distinctProducts) > 1 || $totalQty > 1) {
                                DB::rollBack();
                                Log::warning('Meal type limit violation (per-date)', [
                                    'date' => $date,
                                    'meal_type' => $restrictedType,
                                    'distinct_products' => $distinctProducts,
                                    'total_qty' => $totalQty,
                                ]);
                                return response()->json([
                                    'success' => false,
                                    'message' => 'Only one menu item can be added per meal type for this day',
                                    'meal_type' => $restrictedType,
                                    'date' => $date,
                                ], 422);
                            }
                        }
                    }

                    // Build a minimal validated payload for this date
                    $perDateValidated = $validated;
                    $perDateValidated['delivery_dates'] = [$date];
                    $perDateValidated['selected_days_processed'] = [\Carbon\Carbon::parse($date)->dayOfWeek];
                    $perDateValidated['processed_meals'] = $processedMeals;

                    // Create temp_pre_orders for this date (one for each meal type)
                    $created = $this->createTempPreOrders($orderNo, $perDateValidated, $perDateValidated['selected_days_processed'], $customerDetails, $processedMeals);
                    $tempPreOrderIds = array_merge($tempPreOrderIds, $created);
                }

                dd($tempOrderPaymentIds);

                // Calculate totals
                $totalAmountWithTax = array_sum(array_column($tempPreOrderIds, 'total_amount'));
                $validated['total_amount'] = $totalAmountWithTax;

                // Handle wallet payment processing for per-date model
                $paymentMethod = $validated['payment_method'] ?? 'online';

                if ($paymentMethod === 'wallet') {
                    // Check wallet balance
                    $walletBalance = $this->getCustomerWalletBalance($validated['customer_id']);

                    if ($walletBalance < $totalAmountWithTax) {
                        // Insufficient wallet balance
                        $topUpRequired = $totalAmountWithTax - $walletBalance;
                        DB::rollBack();
                        return response()->json([
                            'success' => false,
                            'message' => 'Insufficient wallet balance',
                            'data' => [
                                'wallet_balance' => $walletBalance,
                                'order_amount' => $totalAmountWithTax,
                                'top_up_required' => $topUpRequired,
                                'can_retry' => true,
                                'payment_options' => [
                                    'wallet_topup_url' => config('services.payment.url') . '/v2/wallet/deposite'
                                ]
                            ]
                        ], 400);
                    }

                    // Sufficient wallet balance - set up for wallet payment
                    $validated['wallet_amount_used'] = $totalAmountWithTax;
                    $validated['gateway_amount'] = 0;
                    $validated['payment_method_final'] = 'wallet';
                } else {
                    // Online payment
                    $validated['wallet_amount_used'] = 0;
                    $validated['gateway_amount'] = $totalAmountWithTax;
                    $validated['payment_method_final'] = 'online';
                }

                // Ensure helpers receive customer context consistently (same as repeating model)
                $validated['customer_details'] = $customerDetails;

                // Use first temp_pre_order for payment processing
                $primaryTempPreOrderId = $tempPreOrderIds[0]['id'];

                // Step 2: Create temp_order_payment records (separate for each meal)
                $tempOrderPaymentIds = $this->createTempOrderPayment($primaryTempPreOrderId, $validated, $tempPreOrderIds);

                // Step 3: Create payment_transaction with status 'initiated'
                $paymentTransactionId = $this->createInitialPaymentTransaction($primaryTempPreOrderId, $orderNo, $validated);

                // Step 3.5: Process wallet deduction if wallet payment is used
                if (($validated['wallet_amount_used'] ?? 0) > 0) {
                    $walletDeductionSuccess = $this->processWalletDeduction(
                        $validated['customer_id'],
                        $validated['wallet_amount_used'],
                        $orderNo,
                        (int)$validated['company_id'],
                        (int)$validated['unit_id']
                    );

                    if (!$walletDeductionSuccess) {
                        DB::rollBack();
                        return response()->json([
                            'success' => false,
                            'message' => 'Wallet deduction failed'
                        ], 500);
                    }
                }

                // Commit before payment service
                DB::commit();

                // Handle payment processing based on payment method
                $paymentServiceResponse = null;

                // If full wallet payment, skip payment service and mark as completed
                if (($validated['payment_method_final'] ?? 'online') === 'wallet') {
                    // Full wallet payment - no need for payment service
                    $paymentServiceResponse = [
                        'success' => true,
                        'data' => [
                            'transaction_id' => 'WALLET_' . $paymentTransactionId,
                            'status' => 'completed',
                            'payment_method' => 'wallet'
                        ]
                    ];

                    Log::info('Wallet payment completed (per-date)', [
                        'order_no' => $orderNo,
                        'payment_transaction_id' => $paymentTransactionId,
                        'wallet_amount' => $validated['wallet_amount_used']
                    ]);

                    // Create final orders since wallet payment is completed
                    $this->createFinalOrdersForWalletPayment($tempPreOrderIds, $validated, $paymentTransactionId);
                } else {
                    // Gateway payment needed - call payment service
                    try {
                        $paymentServiceResponse = $this->initiatePaymentWithPaymentService($orderNo, $validated, $paymentTransactionId);
                    } catch (Exception $e) {
                    Log::error('Payment Service failed after temp records created (per-date)', [
                        'order_no' => $orderNo,
                        'temp_pre_order_ids' => $tempPreOrderIds,
                        'primary_temp_pre_order_id' => $primaryTempPreOrderId,
                        'error' => $e->getMessage()
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Payment initiation failed: ' . $e->getMessage(),
                        'data' => [
                            'order_no' => $orderNo,
                            'temp_pre_order_ids' => $tempPreOrderIds,
                            'can_retry' => true
                        ]
                    ], 500);
                    }
                }

                Log::info('Pre-order (per-date) created successfully', [
                    'order_no' => $orderNo,
                    'temp_pre_order_ids' => $tempPreOrderIds,
                    'primary_temp_pre_order_id' => $primaryTempPreOrderId,
                    'temp_order_payment_ids' => $tempOrderPaymentIds,
                    'payment_transaction_id' => $paymentTransactionId,
                    'customer_id' => $validated['customer_id'],
                    'amount' => $validated['total_amount']
                ]);

                return response()->json([
                    'success' => true,
                    'message' => $responseMessage,
                    'data' => [
                        'temp_pre_order_ids' => $tempPreOrderIds,
                        'primary_temp_pre_order_id' => $primaryTempPreOrderId,
                        'order_no' => $orderNo,
                        'customer_id' => $validated['customer_id'],
                        'customer_name' => $customerDetails['name'],
                        'customer_email' => $customerDetails['email'],
                        'customer_phone' => $customerDetails['phone'],
                        'total_amount' => $validated['total_amount'],
                        'status' => $isWalletPayment ? 'paid' : 'pending',
                        'payment_transaction_id' => $paymentTransactionId,
                        'payment_service_transaction_id' => $paymentServiceResponse['data']['transaction_id'] ?? null,
                        'payment_details' => [
                            'payment_method' => $validated['payment_method_final'] ?? 'online',
                            'wallet_amount_used' => $validated['wallet_amount_used'] ?? 0,
                            'gateway_amount' => $validated['gateway_amount'] ?? $validated['total_amount'],
                            'wallet_payment_completed' => $isWalletPayment
                        ],
                        'payment_urls' => [
                            'process_payment' => config('services.payment.url') . "/api/v2/payments/{$paymentServiceResponse['data']['transaction_id']}/process",
                            'payment_status' => config('services.payment.url') . "/api/v2/payments/{$paymentServiceResponse['data']['transaction_id']}",
                            'order_status' => config('app.url') . "/api/v2/order-management/pre-order-status/{$orderNo}"
                        ]
                    ]
                ], 201);
            }

            // Repeating model (original behavior)
            // Process and validate meals
            $processedMeals = $this->processMeals($validated['meals']);
            if (empty($processedMeals)) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'No valid meals found'
                ], 400);
            }

            Log::info('Processed meals for order creation', [
                'processed_meals' => $processedMeals,
                'count' => count($processedMeals)
            ]);

            // Enforce business rule: Only one menu item can be added per meal type (breakfast/lunch) for this day
            $mealsByType = $this->groupMealsByType($processedMeals);
            foreach (['breakfast', 'lunch'] as $restrictedType) {
                if (!empty($mealsByType[$restrictedType])) {
                    // Count total distinct products and total quantities for this meal type
                    $distinctProducts = array_unique(array_column($mealsByType[$restrictedType], 'product_code'));
                    $totalQty = array_sum(array_map(fn($m) => (int)($m['quantity'] ?? 0), $mealsByType[$restrictedType]));
                    if (count($distinctProducts) > 1 || $totalQty > 1) {
                        DB::rollBack();
                        Log::warning('Meal type limit violation', [
                            'meal_type' => $restrictedType,
                            'distinct_products' => $distinctProducts,
                            'total_qty' => $totalQty,
                        ]);
                        return response()->json([
                            'success' => false,
                            'message' => 'Only one menu item can be added per meal type for this day',
                            'meal_type' => $restrictedType,
                        ], 422);
                    }
                }
            }

            // Process selected days - convert legacy format if needed
            $selectedDays = $this->processSelectedDays($validated);

            // Calculate delivery dates based on start_date, selected_days, and subscription_days
            $deliveryDates = $this->calculateDeliveryDates(
                $validated['start_date'],
                $selectedDays,
                $validated['subscription_days']
            );

            // Add calculated data to validated data
            $validated['delivery_dates'] = $deliveryDates;
            $validated['selected_days_processed'] = $selectedDays;
            $validated['customer_details'] = $customerDetails;
            $validated['processed_meals'] = $processedMeals;
            // Step 1: Create temp_pre_orders records (one for each meal type)
            $tempPreOrderIds = $this->createTempPreOrders($orderNo, $validated, $selectedDays, $customerDetails, $processedMeals);
            dd($temp)

            // Calculate total amount with tax from all temp_pre_orders
            $totalAmountWithTax = array_sum(array_column($tempPreOrderIds, 'total_amount'));
            $validated['total_amount'] = $totalAmountWithTax;

            // Step 1.5: Handle wallet payment processing
            $paymentMethod = $validated['payment_method'] ?? 'online';

            if ($paymentMethod === 'wallet') {
                // Check wallet balance
                $walletBalance = $this->getCustomerWalletBalance($validated['customer_id']);

                if ($walletBalance < $totalAmountWithTax) {
                    // Insufficient wallet balance
                    $topUpRequired = $totalAmountWithTax - $walletBalance;
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'Insufficient wallet balance',
                        'data' => [
                            'wallet_balance' => $walletBalance,
                            'order_amount' => $totalAmountWithTax,
                            'top_up_required' => $topUpRequired,
                            'can_retry' => true,
                            'payment_options' => [
                                'wallet_topup_url' => config('app.url') . '/api/v2/wallet/topup'
                            ]
                        ]
                    ], 400);
                }

                // Sufficient wallet balance - set up for wallet payment
                $validated['wallet_amount_used'] = $totalAmountWithTax;
                $validated['gateway_amount'] = 0;
                $validated['payment_method_final'] = 'wallet';
            } else {
                // Online payment
                $validated['wallet_amount_used'] = 0;
                $validated['gateway_amount'] = $totalAmountWithTax;
                $validated['payment_method_final'] = 'online';
            }

            // Use first temp_pre_order for payment processing
            $primaryTempPreOrderId = $tempPreOrderIds[0]['id'];

            // Step 2: Create temp_order_payment records (separate for each meal)
            $tempOrderPaymentIds = $this->createTempOrderPayment($primaryTempPreOrderId, $validated, $tempPreOrderIds);

            // Step 3: Create payment_transaction with status 'initiated'
            $paymentTransactionId = $this->createInitialPaymentTransaction($primaryTempPreOrderId, $orderNo, $validated);

            // Step 3.5: Process wallet deduction if wallet payment is used
            if (($validated['wallet_amount_used'] ?? 0) > 0) {
                $walletDeductionSuccess = $this->processWalletDeduction(
                    $validated['customer_id'],
                    $validated['wallet_amount_used'],
                    $orderNo,
                    (int)$validated['company_id'],
                    (int)$validated['unit_id']
                );

                if (!$walletDeductionSuccess) {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'Wallet deduction failed'
                    ], 500);
                }
            }

            // Commit transaction before calling Payment Service to avoid deadlocks
            DB::commit();

            // Step 4: Handle payment processing based on payment method
            $paymentServiceResponse = null;

            // If full wallet payment, skip payment service and mark as completed
            if (($validated['payment_method_final'] ?? 'online') === 'wallet') {
                // Full wallet payment - no need for payment service
                $paymentServiceResponse = [
                    'success' => true,
                    'data' => [
                        'transaction_id' => 'WALLET_' . $paymentTransactionId,
                        'status' => 'completed',
                        'payment_method' => 'wallet'
                    ]
                ];

                Log::info('Wallet payment completed', [
                    'order_no' => $orderNo,
                    'payment_transaction_id' => $paymentTransactionId,
                    'wallet_amount' => $validated['wallet_amount_used']
                ]);

                // Create final orders since wallet payment is completed
                $this->createFinalOrdersForWalletPayment($tempPreOrderIds, $validated, $paymentTransactionId);
            } else {
                // Gateway payment needed - call payment service
                try {
                    $paymentServiceResponse = $this->initiatePaymentWithPaymentService($orderNo, $validated, $paymentTransactionId);
                } catch (Exception $e) {
                // If Payment Service fails, mark the temp records as failed but don't rollback
                // The temp records are already created and can be retried later
                Log::error('Payment Service failed after temp records created', [
                    'order_no' => $orderNo,
                    'temp_pre_order_ids' => $tempPreOrderIds,
                    'primary_temp_pre_order_id' => $primaryTempPreOrderId,
                    'error' => $e->getMessage()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Payment initiation failed: ' . $e->getMessage(),
                    'data' => [
                        'order_no' => $orderNo,
                        'temp_pre_order_ids' => $tempPreOrderIds,
                        'can_retry' => true
                    ]
                ], 500);
                }
            }

            Log::info($responseMessage , [
                'order_no' => $orderNo,
                'temp_pre_order_ids' => $tempPreOrderIds,
                'primary_temp_pre_order_id' => $primaryTempPreOrderId,
                'temp_order_payment_ids' => $tempOrderPaymentIds,
                'payment_transaction_id' => $paymentTransactionId,
                'customer_id' => $validated['customer_id'],
                'amount' => $validated['total_amount'],
                'subscription_days' => $validated['subscription_days']
            ]);

            return response()->json([
                'success' => true,
                'message' => $responseMessage,
                'data' => [
                    'temp_pre_order_ids' => $tempPreOrderIds,
                    'primary_temp_pre_order_id' => $primaryTempPreOrderId,
                    'order_no' => $orderNo,
                    'customer_id' => $validated['customer_id'],
                    'customer_name' => $customerDetails['name'],
                    'customer_email' => $customerDetails['email'],
                    'customer_phone' => $customerDetails['phone'],
                    'meals' => $processedMeals,
                    'total_amount' => $validated['total_amount'], // Use the correct total with subscription days and tax
                    'status' => ($validated['payment_method_final'] ?? 'online') === 'wallet' ? 'paid' : 'pending',
                    'days_preference' => implode(',', $selectedDays),
                    'subscription_days' => $validated['subscription_days'],
                    'payment_transaction_id' => $paymentTransactionId,
                    'payment_service_transaction_id' => $paymentServiceResponse['data']['transaction_id'] ?? null,
                    'payment_details' => [
                        'payment_method' => $validated['payment_method_final'] ?? 'online',
                        'wallet_amount_used' => $validated['wallet_amount_used'] ?? 0,
                        'gateway_amount' => $validated['gateway_amount'] ?? $validated['total_amount'],
                        'wallet_payment_completed' => ($validated['payment_method_final'] ?? 'online') === 'wallet'
                    ],
                    'payment_urls' => [
                        'process_payment' => config('services.payment.url') . "/api/v2/payments/{$paymentServiceResponse['data']['transaction_id']}/process",
                        'payment_status' => config('services.payment.url') . "/api/v2/payments/{$paymentServiceResponse['data']['transaction_id']}",
                        'order_status' => config('app.url') . "/api/v2/order-management/pre-order-status/{$orderNo}"
                    ]
                ]
            ], 201);

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Pre-order creation failed', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Pre-order creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order details with payment status
     *
     * @param string $orderNo
     * @return JsonResponse
     */
    public function getOrderDetails(string $orderNo): JsonResponse
    {
        try {
            // Get order
            $order = Order::where('order_no', $orderNo)->first();
            
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            // Get order details
            $orderDetails = OrderDetail::where('ref_order_no', $orderNo)->get();

            // Get payment transaction
            $paymentTransaction = DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'order' => [
                        'order_id' => $order->pk_order_no,
                        'order_no' => $order->order_no,
                        'customer_id' => $order->customer_code,
                        'customer_name' => $order->customer_name,
                        'customer_email' => $order->email_address,
                        'customer_phone' => $order->phone,
                        'customer_address' => $order->ship_address,
                        'product_name' => $order->product_name,
                        'product_type' => $order->product_type,
                        'quantity' => $order->quantity,
                        'amount' => $order->amount,
                        'order_status' => $order->order_status,
                        'payment_mode' => $order->payment_mode,
                        'amount_paid' => $order->amount_paid,
                        'days_preference' => $order->days_preference,
                        'delivery_status' => $order->delivery_status,
                        'order_date' => $order->order_date,
                        'delivery_time' => $order->delivery_time,
                        'delivery_end_time' => $order->delivery_end_time,
                        'recurring_status' => $order->recurring_status,
                    ],
                    'meal_items' => $orderDetails->map(function ($detail) {
                        return [
                            'product_code' => $detail->product_code,
                            'product_name' => $detail->product_name,
                            'quantity' => $detail->quantity,
                            'amount' => $detail->product_amount,
                        ];
                    }),
                    'payment_transaction' => $paymentTransaction ? [
                        'transaction_id' => $paymentTransaction->pk_transaction_id,
                        'gateway_transaction_id' => $paymentTransaction->gateway_transaction_id,
                        'status' => $paymentTransaction->status,
                        'gateway' => $paymentTransaction->gateway,
                        'payment_amount' => $paymentTransaction->payment_amount,
                        'created_date' => $paymentTransaction->created_date,
                    ] : null
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get order details', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get order details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get customer orders categorized by status (upcoming, cancelled, other)
     *
     * @param Request $request
     * @param int $customerId
     * @return JsonResponse
     */
    public function getCustomerOrders(GetCustomerOrdersRequest $request, int $customerId): JsonResponse
    {
        try {
            // Get filter parameters
            $studentNameFilter = $request->input('student_name_filter');
            $includeCancelled = $request->boolean('include_cancelled', false); // Default: include cancelled orders
            $orderStatus = $request->input('order_status');
            // Date range filters (YYYY-MM-DD)
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            // Pagination controls (global)
            $perPage = (int) $request->query('per_page', 10);
            $page = (int) $request->query('page', 1);

            // Extract order_no from order_status if present (format: "status?order_no=VALUE")
            $specificOrderNo = null;
            if ($orderStatus && strpos($orderStatus, '?order_no=') !== false) {
                $parts = explode('?order_no=', $orderStatus);
                $specificOrderNo = $parts[1] ?? null;
                $orderStatus = $parts[0]; // Keep the actual status part
            }

            // Validate customer exists
            $customer = DB::table('customers')
                ->where('pk_customer_code', $customerId)
                ->where('status', 1)
                ->first(['pk_customer_code', 'customer_name', 'email_address', 'phone']);

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Priority handling: If specific order_no is requested, fetch it first
            if ($specificOrderNo) {
                return $this->getSpecificOrderFirst($customerId, $specificOrderNo, $customer, $request);
            }
            // Build optimized query with filters
            $query = $this->buildOptimizedOrderQuery($customerId, $studentNameFilter, $includeCancelled, $orderStatus, $startDate, $endDate);

            $orders = $query->get();

            Log::info('Retrieved orders for customer', [
                'customer_id' => $customerId,
                'orders_count' => count($orders),
                'include_cancelled' => $includeCancelled,
                'order_status_filter' => $orderStatus,
                'student_name_filter' => $studentNameFilter
            ]);

            // Group orders by order_id and categorize
            $groupedOrders = $this->groupAndCategorizeOrders($orders);

            // Global pagination across all orders
            $allPaginated = $this->paginateArray($groupedOrders['all'], $page, $perPage);

            // Extract unique student names from each category
            $studentNames = $this->extractStudentNames($groupedOrders);

            return response()->json([
                'success' => true,
                'data' => [
                    'customer' => [
                        'customer_id' => $customer->pk_customer_code,
                        'name' => $customer->customer_name,
                        'email' => $customer->email_address,
                        'phone' => $customer->phone
                    ],
                    'summary' => [
                        'total_orders' => count($groupedOrders['all']),
                        'upcoming_orders' => count($groupedOrders['upcoming']),
                        'cancelled_orders' => count($groupedOrders['cancelled']),
                        'other_orders' => count($groupedOrders['other'])
                    ],
                    'student_names' => $studentNames,
                    'filters' => [
                        'student_name_filter' => $studentNameFilter,
                        'include_cancelled' => $includeCancelled,
                        'order_status' => $orderStatus,
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'per_page' => $perPage,
                        'page' => $page
                    ],
                    'orders' => [
                        // Categories returned unpaginated
                        'upcoming' => $groupedOrders['upcoming'],
                        'cancelled' => $groupedOrders['cancelled'],
                        'other' => $groupedOrders['other']
                    ],
                    'pagination' => [
                        'all' => $allPaginated['meta']
                    ]
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get customer orders', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve customer orders'
            ], 500);
        }
    }

    /**
     * Create temp_pre_orders records (one for each meal type)
     *
     * @param string $orderNo
     * @param array $validated
     * @param array $selectedDays
     * @param array $customerDetails
     * @param array $processedMeals
     * @return array
     */
    protected function createTempPreOrders(string $orderNo, array $validated, array $selectedDays, array $customerDetails, array $processedMeals): array
    {
        // Get tax settings
        $taxSettings = $this->getTaxSettings();
        $taxRate = $taxSettings['rate'];
        $applyTax = $taxSettings['apply'];

        // Group meals by meal type (breakfast, lunch, etc.)
        $mealsByType = $this->groupMealsByType($processedMeals);

        $tempPreOrderIds = [];
        $mealTypeIndex = 0;

        foreach ($mealsByType as $mealType => $meals) {
            // Calculate daily meal amount
            $mealAmount = array_sum(array_column($meals, 'total_amount'));

            // Calculate tax on the full subscription amount
            $taxAmount = $applyTax ? round($mealAmount * $taxRate / 100, 2) : 0;
            
            // Express extra delivery charge logic (once per temp_pre_order)
            $kitchenId = (int)$validated['fk_kitchen_code'];
            $deliveryCharge = 0.00;
            $isExpress = (bool)($validated['is_express'] ?? false);
            if ($isExpress) {
                $cutoff = $this->getCutOffSettings((int)$validated['company_id'], $kitchenId, strtolower($mealType));
                $withinWindow = $this->isWithinExpressWindow(
                    $cutoff['cut_off_time'] ?? '00:00:00',
                    isset($cutoff['cut_off_day']) ? (int)$cutoff['cut_off_day'] : null,
                    strtolower($mealType),
                    (int)$validated['company_id']
                );
                if ($withinWindow) {
                    $deliveryCharge = $this->getExpressExtraCharge((int)$validated['company_id'], $kitchenId, strtolower($mealType));
                }
                Log::info('Express evaluation for meal type', [
                    'meal_type' => $mealType,
                    'cutoff_time' => $cutoff['cut_off_time'] ?? null,
                    'cutoff_day' => $cutoff['cut_off_day'] ?? null,
                    'within_window' => $withinWindow,
                    'extra_delivery_charge' => $deliveryCharge,
                ]);
            }

            $totalAmount = $mealAmount + $taxAmount + $deliveryCharge;


            // Generate unique order number for each meal type
            $mealOrderNo = $mealTypeIndex === 0 ? $orderNo : $this->generateOrderNumber();

            // Create item_preference JSON like the example
            $itemPreference = $this->createItemPreferenceJson($meals);

            Log::info('Creating temp pre-order for meal type', [
                'meal_type' => $mealType,
                'meal_order_no' => $mealOrderNo,
                'meals' => $meals,
                'daily_meal_amount' => $mealAmount,
                'delivery_dates_count' => count($validated['delivery_dates']),
                'meal_amount' => $mealAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount
            ]);

            $tempPreOrderId = DB::table('temp_pre_orders')->insertGetId([
                'company_id' => (int)$validated['company_id'],
                'unit_id' => (int)$validated['unit_id'],
                'fk_kitchen_code' => (int)$validated['fk_kitchen_code'],
                'ref_order' => 0,
                'order_no' => $mealOrderNo,
                'auth_id' => $validated['customer_id'],
                'customer_code' => $validated['customer_id'],
                'customer_name' => $customerDetails['name'],
                'food_preference' => '[]', // Fixed: Should be empty array as string
                'phone' => $customerDetails['phone'],
                'email_address' => $customerDetails['email'],
                'location_code' => $validated['location_code'],
                'location_name' => $validated['location_name'],
                'city' => $validated['city'], // Keep original city ID (e.g., 2)
                'city_name' => $this->getCityNameById($validated['city']), // Get actual city name (e.g., "Mumbai")
                'product_code' => $meals[0]['product_code'], // Use first meal of this type
                'product_name' => $this->getActualProductName($meals, $mealType),
                'product_description' => $this->getActualProductDescription($meals, $mealType),
                'product_type' => 'Meal',
                'quantity' => array_sum(array_column($meals, 'quantity')),
                'order_type' => 'Day',
                'order_days' => implode(',', $validated['delivery_dates']), // Fixed: Store as CSV string
                'product_price' => $mealAmount,
                'amount' => $mealAmount,
                'total_amt' => $totalAmount,
                'tax' => $taxAmount,
                'total_tax' => $taxAmount,
                'delivery_charges' => $deliveryCharge,
                'service_charges' => 0.00,
                'total_delivery_charges' => $deliveryCharge,
                'line_delivery_charges' => 0.00,
                'applied_discount' => 0.00,
                'total_applied_discount' => 0.00,
                'order_status' => 'New',
                'order_date' => now()->format('Y-m-d'),
                'due_date' => null, // Fixed: Should be NULL
                'ship_address' => $validated['customer_address'],
                'order_menu' => strtolower($mealType),
                'invoice_status' => 'Unbill',
                'amount_paid' => 0,
                'inventory_type' => 'perishable',
                'food_type' => $validated['food_preference'] ?? 'veg',
                'total_third_party_charges' => 0.00,
                'order_for' => 'fixed',
                'PRODUCT_MEAL_CALENDAR' => 0,
                'delivery_type' => $isExpress ? 'express' : 'delivery',
                'delivery_person' => null,
                'payment_mode' => 'online',
                'days_preference' => implode(',', $selectedDays),
                'tp_delivery' => null,
                'tp_delivery_charges' => null,
                'tp_delivery_charges_type' => null,
                'tp_aggregator' => null,
                'tp_aggregator_charges' => null,
                'tp_aggregator_charges_type' => null,
                'tax_method' => 'exclusive',
                'apply_tax' => $applyTax ? 'yes' : 'no',
                'source' => 'api',
                'delivery_time' => null, // Fixed: Should be NULL
                'delivery_end_time' => null, // Fixed: Should be NULL
                'item_preference' => $itemPreference,
                'remark' => null,
                'recurring_status' => 1,
                'delivery_note' => null,
            ]);

            $tempPreOrderIds[] = [
                'id' => $tempPreOrderId,
                'order_no' => $mealOrderNo,
                'meal_type' => $mealType,
                'amount' => $mealAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'delivery_charges' => $deliveryCharge,
                'meals_count' => count($meals)
            ];

            Log::info('Temp pre-order created for meal type', [
                'temp_pre_order_id' => $tempPreOrderId,
                'meal_type' => $mealType,
                'order_no' => $mealOrderNo,
                'customer_id' => $validated['customer_id'],
                'amount' => $mealAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount
            ]);

            $mealTypeIndex++;
        }

        return $tempPreOrderIds;
    }

    /**
     * Check if current time is within express window for same-day orders.
     * - Normal case: 00:00:00 to cutoff time (same day)
     * - Special case: if cutoff time is exactly 00:00:00 (midnight), allow till configurable extended end time (default 08:00:00)
     *   for SAME-DAY only. Controlled via settings and company scope.
     */
    protected function isWithinExpressWindow(string $cutoffTime, ?int $cutoffDay, string $mealType, ?int $companyId = null): bool
    {
        try {
            $nowTime = now()->format('H:i:s');
            Log::info('ExpressWindowCheck:start', [
                'now' => $nowTime,
                'cutoff_time' => $cutoffTime,
                'cutoff_day' => $cutoffDay,
                'meal_type' => $mealType,
                'company_id' => $companyId,
            ]);

            // If DB has midnight as cutoff, allow until EXPRESS_EXTENDED_END_TIME (default 08:00:00)
            if ($cutoffTime === '00:00:00') {
                $extendedEnabled = strtolower((string) $this->getSettingValueFlexible('EXPRESS_EXTENDED_ENABLED', 'yes', $companyId));
                if ($extendedEnabled === 'yes') {
                    $extendedEnd = (string) $this->getSettingValueFlexible('EXPRESS_EXTENDED_END_TIME', '08:00:00', $companyId);
                    $within = strcmp($nowTime, '00:00:00') >= 0 && strcmp($nowTime, $extendedEnd) <= 0;
                    Log::info('ExpressWindowCheck:extended', [
                        'extended_enabled' => $extendedEnabled,
                        'extended_end' => $extendedEnd,
                        'within' => $within,
                    ]);
                    return $within;
                }
                Log::info('ExpressWindowCheck:extended_disabled');
                return false;
            }

            // Default window: midnight to cutoff time on the same day
            $withinNormal = strcmp($nowTime, '00:00:00') >= 0 && strcmp($nowTime, $cutoffTime) <= 0;
            Log::info('ExpressWindowCheck:normal', [
                'within' => $withinNormal,
            ]);
            return $withinNormal;
        } catch (\Exception $e) {
            Log::warning('Failed to evaluate express window, defaulting to false', [
                'cutoff_time' => $cutoffTime,
                'cutoff_day' => $cutoffDay,
                'meal_type' => $mealType,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get express extra delivery charge for a kitchen and meal type from settings (>= 0)
     */
    protected function getExpressExtraDeliveryCharge(int $kitchenId, string $mealType, ?int $companyId = null): float
    {
        $mealTypeUpper = strtoupper($mealType);
        $key = "K{$kitchenId}_{$mealTypeUpper}_EXPRESS_EXTRA_DELIVERY_CHARGE";
        $raw = $this->getSettingValueFlexible($key, '0', $companyId);
        Log::info('ExpressChargeFetch', [
            'key' => $key,
            'value' => $raw,
            'company_id' => $companyId,
        ]);
        if ($raw === null) {
            return 0.0;
        }
        $val = (float) $raw;
        return max(0.0, $val);
    }
    protected function getSettingValueFlexible(string $key, $default = null, ?int $companyId = null)
    {
        try {
            $query = DB::table('settings');

            // Prefer company-scoped value if column exists
            $hasCompany = Schema::hasColumn('settings', 'company_id');
            if ($hasCompany && $companyId !== null) {
                $query = $query->where('company_id', $companyId);
            }

            // Try modern columns first
            $value = null;
            if (Schema::hasColumn('settings', 'key') && Schema::hasColumn('settings', 'value')) {
                $value = DB::table('settings')
                    ->when($hasCompany && $companyId !== null, function ($q) use ($companyId) {
                        return $q->where('company_id', $companyId);
                    })
                    ->where('key', $key)
                    ->value('value');
            }

            if ($value === null && Schema::hasColumn('settings', 'setting_key') && Schema::hasColumn('settings', 'setting_value')) {
                $value = DB::table('settings')
                    ->when($hasCompany && $companyId !== null, function ($q) use ($companyId) {
                        return $q->where('company_id', $companyId);
                    })
                    ->where('setting_key', $key)
                    ->value('setting_value');
            }

            return $value !== null ? $value : $default;
        } catch (\Exception $e) {
            Log::warning('Failed to fetch setting value (flexible)', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);
            return $default;
        }
    }

    /**
     * Create temp_order_payment records (separate record for each meal)
     *
     * @param int $tempPreOrderId
     * @param array $validated
     * @param array $tempPreOrderIds
     * @return array
     */
    protected function createTempOrderPayment(int $tempPreOrderId, array $validated, array $tempPreOrderIds): array
    {
        $tempOrderPaymentIds = [];

        // Create separate temp_order_payment record for each meal (as per older records pattern)
        foreach ($tempPreOrderIds as $tempPreOrder) {
            $tempOrderPaymentId = DB::table('temp_order_payment')->insertGetId([
                'company_id' => (int)$validated['company_id'],
                'unit_id' => (int)$validated['unit_id'],
                'temp_order_id' => 0, // Will be updated when actual orders are created
                'temp_preorder_id' => $tempPreOrder['id'],
                'amount' => $tempPreOrder['amount'], // Base amount without tax for this specific meal
                'status' => 'pending',
                'date' => now()->format('Y-m-d'),
                'type' => 'online',
                'istodaysorder' => '0',
                'order_menu' => $tempPreOrder['meal_type'], // Specific meal type for this record
                'recurring_status' => '1',
            ]);

            $tempOrderPaymentIds[] = $tempOrderPaymentId;

            Log::info('Temp order payment created for individual meal', [
                'temp_order_payment_id' => $tempOrderPaymentId,
                'temp_pre_order_id' => $tempPreOrder['id'],
                'amount' => $tempPreOrder['amount'],
                'order_menu' => $tempPreOrder['meal_type']
            ]);
        }

        Log::info('All temp order payments created', [
            'temp_order_payment_ids' => $tempOrderPaymentIds,
            'total_records' => count($tempOrderPaymentIds),
            'total_amount' => array_sum(array_column($tempPreOrderIds, 'amount'))
        ]);

        return $tempOrderPaymentIds;
    }

    /**
     * Determine primary meal type from temp_pre_orders
     *
     * @param array $tempPreOrderIds
     * @return string
     */
    protected function determinePrimaryMealType(array $tempPreOrderIds): string
    {
        // If only one meal type, return it
        if (count($tempPreOrderIds) === 1) {
            return $tempPreOrderIds[0]['meal_type'];
        }

        // If multiple meal types, prioritize by common order: breakfast > lunch > dinner > snack
        $mealTypePriority = ['breakfast' => 1, 'lunch' => 2, 'dinner' => 3, 'snack' => 4, 'meal' => 5];

        $availableMealTypes = array_column($tempPreOrderIds, 'meal_type');

        // Sort by priority and return the highest priority meal type
        usort($availableMealTypes, function($a, $b) use ($mealTypePriority) {
            $priorityA = $mealTypePriority[$a] ?? 99;
            $priorityB = $mealTypePriority[$b] ?? 99;
            return $priorityA <=> $priorityB;
        });

        $primaryMealType = $availableMealTypes[0];

        Log::info('Determined primary meal type', [
            'available_meal_types' => $availableMealTypes,
            'primary_meal_type' => $primaryMealType
        ]);

        return $primaryMealType;
    }

    /**
     * Create initial payment_transaction record with status 'initiated'
     *
     * @param int $tempPreOrderId
     * @param string $orderNo
     * @param array $validated
     * @return int
     */
    protected function createInitialPaymentTransaction(int $tempPreOrderId, string $orderNo, array $validated): int
    {
        // Get wallet and gateway amounts
        $walletAmount = $validated['wallet_amount_used'] ?? 0;
        $gatewayAmount = $validated['gateway_amount'] ?? $validated['total_amount'];
        $paymentMethod = $validated['payment_method_final'] ?? 'online';

        $paymentTransactionId = DB::table('payment_transaction')->insertGetId([
            'company_id' => (int)$validated['company_id'],
            'unit_id' => (int)$validated['unit_id'],
            'customer_id' => $validated['customer_id'],
            'customer_email' => $validated['customer_details']['email'],
            'customer_phone' => $validated['customer_details']['phone'],
            'customer_name' => $validated['customer_details']['name'],
            'payment_amount' => $gatewayAmount, // Amount to be paid via gateway
            'transaction_charges' => round($gatewayAmount * 0.03, 2), // 3% transaction fee on gateway amount only
            'wallet_amount' => $walletAmount, // Amount paid from wallet
            'pre_order_id' => $orderNo,
            'gateway' => $paymentMethod === 'wallet' ? 'wallet' : 'initiated',
            'status' => $paymentMethod === 'wallet' ? 'completed' : 'initiated', // Wallet payments are immediately completed
            'gateway_transaction_id' => $paymentMethod === 'wallet' ? 'WALLET_' . time() : null,
            'description' => $this->getPaymentDescription($paymentMethod, $walletAmount, $gatewayAmount),
            'created_date' => now(),
            'transaction_by' => 'api',
            'referer' => 'order_api',
            'success_url' => config('app.url') . "/api/v2/order-management/payment-success/{$orderNo}",
            'failure_url' => config('app.url') . "/api/v2/order-management/payment-failure/{$orderNo}",
            'context' => 'order_payment',
            'discount' => 0.00,
            'recurring' => 1,
        ]);

        Log::info('Payment transaction initiated', [
            'payment_transaction_id' => $paymentTransactionId,
            'temp_pre_order_id' => $tempPreOrderId,
            'order_no' => $orderNo,
            'amount' => $validated['total_amount']
        ]);

        return $paymentTransactionId;
    }

    /**
     * Generate order days based on days preference and subscription duration
     *
     * @param string $daysPreference
     * @param int $subscriptionDays
     * @return string
     */
    protected function generateOrderDays(string $daysPreference, int $subscriptionDays): string
    {
        $selectedDays = explode(',', $daysPreference);
        $orderDays = [];

        $startDate = now();
        $endDate = now()->addDays($subscriptionDays);

        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dayOfWeek = $date->dayOfWeek; // 0=Sunday, 1=Monday, etc.

            if (in_array((string)$dayOfWeek, $selectedDays)) {
                $orderDays[] = $date->format('Y-m-d');
            }
        }

        return json_encode($orderDays);
    }

    /**
     * Generate unique order number in the format: 4 random chars + YYMMDD
     * Example: 2UO3250702 (matches existing pattern)
     *
     * @return string
     */
    protected function generateOrderNumber(): string
    {
        // Generate 4 random characters (letters and numbers)
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $random = '';
        for ($i = 0; $i < 4; $i++) {
            $random .= $characters[rand(0, strlen($characters) - 1)];
        }

        // Add date in YYMMDD format
        $dateStr = now()->format('ymd');

        return $random . $dateStr;
    }

    /**
     * Create payment transaction record and initiate payment with payment service
     *
     * @param Order $order
     * @param array $validated
     * @return array
     */
    protected function createPaymentTransaction(Order $order, array $validated): array
    {
        // Create local payment transaction record for tracking
        $transactionId = DB::table('payment_transaction')->insertGetId([
            'company_id' => (int)$validated['company_id'],
            'unit_id' => (int)$validated['unit_id'],
            'customer_id' => $validated['customer_id'],
            'customer_email' => $validated['customer_email'],
            'customer_phone' => $validated['customer_phone'],
            'customer_name' => $validated['customer_name'],
            'payment_amount' => $validated['amount'],
            'transaction_charges' => round($validated['amount'] * 0.03, 2), // 3% transaction fee
            'wallet_amount' => 0.00,
            'pre_order_id' => $order->order_no,
            'gateway' => 'pending',
            'status' => 'pending',
            'gateway_transaction_id' => null,
            'description' => "Payment for {$validated['product_name']} subscription",
            'created_date' => now(),
            'transaction_by' => 'api',
            'referer' => 'order_api',
            'success_url' => config('app.url') . "/api/v2/order-management/payment-success/{$order->order_no}",
            'failure_url' => config('app.url') . "/api/v2/order-management/payment-failure/{$order->order_no}",
            'context' => 'order_payment',
            'discount' => 0.00,
            'recurring' => 1,
        ]);

        // Initiate payment with payment service
        $paymentServiceResponse = $this->initiatePaymentWithPaymentService($order, $validated, $transactionId);

        return [
            'local_transaction_id' => $transactionId,
            'payment_service_response' => $paymentServiceResponse
        ];
    }

    /**
     * Initiate payment with payment service v12
     *
     * @param string $orderNo
     * @param array $validated
     * @param int $localTransactionId
     * @return array
     */
    protected function initiatePaymentWithPaymentService(string $orderNo, array $validated, int $localTransactionId): array
    {
        try {
            $paymentServiceUrl = config('services.payment.url', 'http://localhost:8002');

            $paymentData = [
                'customer_id' => $validated['customer_id'],
                'customer_email' => $validated['customer_details']['email'],
                'customer_phone' => $validated['customer_details']['phone'],
                'customer_name' => $validated['customer_details']['name'],
                'amount' => $validated['total_amount'],
                'transaction_charges' => round($validated['total_amount'] * 0.03, 2),
                'wallet_amount' => 0.00,
                'order_id' => $orderNo, // Use order_no as order_id for payment service
                'referer' => 'quickserve_order_api',
                'success_url' => config('app.url') . "/api/v2/order-management/payment-success/{$orderNo}",
                'failure_url' => config('app.url') . "/api/v2/order-management/payment-failure/{$orderNo}",
                'context' => 'order_payment',
                'recurring' => true,
                'discount' => 0.00
            ];

            // Call payment service to initiate payment (optimized for speed)
            $response = Http::timeout(15) // Reduced timeout
                ->retry(2, 500) // Reduced retries and delay
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-Service' => 'quickserve-v12',
                    'X-Order-Transaction-ID' => $localTransactionId
                ])
                ->post("{$paymentServiceUrl}/api/v2/payments", $paymentData);

            Log::info('Payment service call completed', [
                'order_no' => $orderNo,
                'status_code' => $response->status(),
                'successful' => $response->successful()
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                // Update local transaction with payment service transaction ID
                DB::table('payment_transaction')
                    ->where('pk_transaction_id', $localTransactionId)
                    ->update([
                        'gateway_transaction_id' => $responseData['data']['transaction_id'] ?? null,
                        'modified_date' => now()
                    ]);

                Log::info('Payment initiated with payment service', [
                    'order_no' => $orderNo,
                    'local_transaction_id' => $localTransactionId,
                    'payment_service_transaction_id' => $responseData['data']['transaction_id'] ?? null
                ]);

                return $responseData;
            } else {
                throw new Exception('Payment service initiation failed: ' . $response->body());
            }

        } catch (Exception $e) {
            Log::error('Failed to initiate payment with payment service', [
                'order_no' => $orderNo,
                'local_transaction_id' => $localTransactionId,
                'error' => $e->getMessage()
            ]);

            // Update local transaction status to failed
            DB::table('payment_transaction')
                ->where('pk_transaction_id', $localTransactionId)
                ->update([
                    'status' => 'failed',
                    'description' => 'Payment service initiation failed: ' . $e->getMessage(),
                    'modified_date' => now()
                ]);

            throw $e;
        }
    }

    /**
     * Handle payment success callback from payment service
     * Creates actual orders and order details from temp tables
     *
     * @param Request $request
     * @param string $orderNo
     * @return JsonResponse
     */
    public function handlePaymentSuccess(Request $request, string $orderNo): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Find the primary temp pre-order
            $tempPreOrder = DB::table('temp_pre_orders')->where('order_no', $orderNo)->first();

            if (!$tempPreOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pre-order not found'
                ], 404);
            }

            // Find all related temp_pre_orders for the same customer created together
            // This handles multiple meal types (breakfast, lunch) created in the same transaction
            $allRelatedTempPreOrders = DB::table('temp_pre_orders')
                ->where('customer_code', $tempPreOrder->customer_code)
                ->where('order_date', $tempPreOrder->order_date)
                ->whereBetween('last_modified', [
                    date('Y-m-d H:i:s', strtotime($tempPreOrder->last_modified) - 120), // 2 minutes before
                    date('Y-m-d H:i:s', strtotime($tempPreOrder->last_modified) + 120)  // 2 minutes after
                ])
                ->get();

            Log::info('Found related temp pre-orders for payment processing', [
                'primary_order_no' => $orderNo,
                'related_orders_count' => count($allRelatedTempPreOrders),
                'related_order_nos' => array_column($allRelatedTempPreOrders->toArray(), 'order_no')
            ]);

            // Step 1: Update payment_transaction with success details
            $this->updatePaymentTransactionSuccess($orderNo, $request);

            // Step 2: Create payment_transfered record if gateway is Razorpay
            if (strtolower($request->input('gateway', '')) === 'razorpay') {
                $this->createPaymentTransferRecord($orderNo, $request);
            }

            // Step 3: Update temp_order_payment status to success for primary order
            $this->updateTempOrderPaymentSuccess($tempPreOrder->pk_order_no);

            // Step 4: Create actual orders and order details from ALL related temp data
            $allCreatedOrders = [];
            foreach ($allRelatedTempPreOrders as $relatedTempPreOrder) {
                $createdOrders = $this->createActualOrdersFromTemp($relatedTempPreOrder, $request);
                $allCreatedOrders = array_merge($allCreatedOrders, $createdOrders);

                Log::info('Created orders from related temp pre-order', [
                    'temp_order_no' => $relatedTempPreOrder->order_no,
                    'meal_type' => $relatedTempPreOrder->order_menu,
                    'orders_created' => count($createdOrders)
                ]);
            }

            // Step 5: Create wallet debit entries for the payment
            $this->createWalletDebitEntries($tempPreOrder, $allRelatedTempPreOrders, $request);

            DB::commit();

            Log::info('Payment success processed and orders created', [
                'primary_order_no' => $orderNo,
                'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                'related_temp_orders_count' => count($allRelatedTempPreOrders),
                'total_created_orders' => count($allCreatedOrders),
                'payment_service_transaction_id' => $request->input('payment_service_transaction_id')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment success processed and orders created',
                'data' => [
                    'primary_order_no' => $orderNo,
                    'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                    'related_temp_orders_count' => count($allRelatedTempPreOrders),
                    'created_orders_count' => count($allCreatedOrders),
                    'created_order_details_count' => $this->countOrderDetails($allCreatedOrders),
                    'payment_status' => 'completed',
                    'orders_created' => array_slice($allCreatedOrders, 0, 10) // Show first 10 orders
                ]
            ]);

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to process payment success', [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment success: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment failure callback from payment service
     *
     * @param Request $request
     * @param string $orderNo
     * @return JsonResponse
     */
    public function handlePaymentFailure(Request $request, string $orderNo): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Find the temp pre-order
            $tempPreOrder = DB::table('temp_pre_orders')->where('order_no', $orderNo)->first();

            if (!$tempPreOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pre-order not found'
                ], 404);
            }

            // Update payment_transaction with failure details
            DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->update([
                    'status' => 'failed',
                    'gateway' => $request->input('gateway', 'online'),
                    'gateway_transaction_id' => $request->input('payment_service_transaction_id'),
                    'description' => 'Payment failed: ' . $request->input('failure_reason', 'Payment gateway failure'),
                    'modified_date' => now()
                ]);

            // Update temp_order_payment status to failed (keep as pending for retry)
            DB::table('temp_order_payment')
                ->where('temp_preorder_id', $tempPreOrder->pk_order_no)
                ->update([
                    'status' => 'pending' // Keep as pending to allow retry
                ]);

            // Update temp_pre_orders status
            DB::table('temp_pre_orders')
                ->where('pk_order_no', $tempPreOrder->pk_order_no)
                ->update([
                    'order_status' => 'Payment Failed'
                ]);

            DB::commit();

            Log::warning('Payment failure processed for pre-order', [
                'order_no' => $orderNo,
                'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                'failure_reason' => $request->input('failure_reason', 'Payment gateway failure')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment failure processed',
                'data' => [
                    'order_no' => $orderNo,
                    'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                    'order_status' => 'Payment Failed',
                    'payment_status' => 'failed',
                    'failure_reason' => $request->input('failure_reason', 'Payment gateway failure'),
                    'retry_available' => true
                ]
            ]);

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to process payment failure', [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment failure: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pre-order status
     *
     * @param string $orderNo
     * @return JsonResponse
     */
    public function getPreOrderStatus(string $orderNo): JsonResponse
    {
        try {
            // Get temp pre-order
            $tempPreOrder = DB::table('temp_pre_orders')->where('order_no', $orderNo)->first();

            if (!$tempPreOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pre-order not found'
                ], 404);
            }

            // Get temp order payment
            $tempOrderPayment = DB::table('temp_order_payment')
                ->where('temp_preorder_id', $tempPreOrder->pk_order_no)
                ->first();

            // Get payment transaction
            $paymentTransaction = DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->first();

            // Count created orders if payment was successful
            $createdOrdersCount = 0;
            $createdOrderDetailsCount = 0;

            if ($tempOrderPayment && $tempOrderPayment->status === 'success') {
                $createdOrdersCount = DB::table('orders')
                    ->where('ref_order', $tempPreOrder->pk_order_no)
                    ->count();

                $createdOrderDetailsCount = DB::table('order_details')
                    ->whereIn('ref_order_no', function($query) use ($tempPreOrder) {
                        $query->select('order_no')
                              ->from('orders')
                              ->where('ref_order', $tempPreOrder->pk_order_no);
                    })
                    ->count();
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'pre_order' => [
                        'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                        'order_no' => $tempPreOrder->order_no,
                        'customer_id' => $tempPreOrder->customer_code,
                        'customer_name' => $tempPreOrder->customer_name,
                        'product_name' => $tempPreOrder->product_name,
                        'amount' => $tempPreOrder->amount,
                        'order_status' => $tempPreOrder->order_status,
                        'days_preference' => $tempPreOrder->days_preference,
                        'order_days' => is_string($tempPreOrder->order_days) &&
                                       json_decode($tempPreOrder->order_days) !== null ?
                                       json_decode($tempPreOrder->order_days) :
                                       [$tempPreOrder->order_days],
                        'created_date' => $tempPreOrder->last_modified
                    ],
                    'payment_status' => [
                        'temp_payment_status' => $tempOrderPayment->status ?? 'unknown',
                        'transaction_status' => $paymentTransaction->status ?? 'unknown',
                        'gateway' => $paymentTransaction->gateway ?? null,
                        'gateway_transaction_id' => $paymentTransaction->gateway_transaction_id ?? null
                    ],
                    'orders_created' => [
                        'count' => $createdOrdersCount,
                        'order_details_count' => $createdOrderDetailsCount,
                        'status' => $createdOrdersCount > 0 ? 'completed' : 'pending'
                    ]
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get pre-order status', [
                'order_no' => $orderNo,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get pre-order status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update payment_transaction with success details
     *
     * @param string $orderNo
     * @param Request $request
     * @return void
     */
    protected function updatePaymentTransactionSuccess(string $orderNo, Request $request): void
    {
        // Update the QuickServe transaction (referer = 'order_api') to completed
        $updatedRows = DB::table('payment_transaction')
            ->where('pre_order_id', $orderNo)
            ->where('referer', 'order_api') // Only update QuickServe transaction
            ->update([
                'status' => 'completed',
                'gateway' => $request->input('gateway', 'online'),
                'gateway_transaction_id' => $request->input('payment_service_transaction_id'),
                'description' => 'Payment completed successfully',
                'modified_date' => now()
            ]);

        Log::info('Payment transaction updated to completed', [
            'order_no' => $orderNo,
            'updated_rows' => $updatedRows,
            'gateway' => $request->input('gateway'),
            'gateway_transaction_id' => $request->input('payment_service_transaction_id')
        ]);
    }

    /**
     * Create payment_transfered record for Razorpay payments
     *
     * @param string $orderNo
     * @param Request $request
     * @return void
     */
    protected function createPaymentTransferRecord(string $orderNo, Request $request): void
    {
        // Get payment transaction details (QuickServe transaction)
        $paymentTransaction = DB::table('payment_transaction')
            ->where('pre_order_id', $orderNo)
            ->where('referer', 'order_api') // Get QuickServe transaction
            ->first();
        if (!$paymentTransaction) {
            Log::warning('No payment_transaction found to create transfer record', [
                'order_no' => $orderNo
            ]);
            return;
        }

        $companyId = (int)($request->input('company_id') ?? ($paymentTransaction->company_id ?? 0));
        $unitId = (int)($request->input('unit_id') ?? ($paymentTransaction->unit_id ?? 0));

        $transferId = 'trf_' . uniqid();
        // Get recipient account (merchant sub-account) from settings
        $recipientAccount = DB::table('settings')
            ->where('key', 'GATEWAY_RAZORPAY_MERCHANT_ACCOUNT_NO')
            ->value('value');

        // Persist request/razorpay identifiers as JSON in description for traceability
        $description = json_encode([
            'razorpay_order_id' => $request->input('razorpay_order_id'),
            'razorpay_payment_id' => $request->input('razorpay_payment_id'),
            'razorpay_signature' => $request->input('razorpay_signature'),
        ]);

        DB::table('payment_transfered')->insert([
            'pk_transfer_id'      => $transferId,
            'company_id'          => $companyId,
            'unit_id'             => $unitId,
            'fk_transaction_id'   => $paymentTransaction->pk_transaction_id ?? null,
            // Razorpay transfer object uses payment id as source
            'source'              => $request->input('payment_service_transaction_id'),
            // Recipient is the merchant (linked) account id from settings
            'recipient'           => $recipientAccount,
            'amount'              => $paymentTransaction->payment_amount,
            'currency'            => 'INR',
            'amount_reversed'     => 0,
            // When the actual transfer is created via Razorpay API, this can be updated to their created_at
            'transfered_at'       => now(),
            'created_date'        => date('Y-m-d H:i:s'),
            'description'         => $description,
        ]);

        Log::info('Payment transfer record created', [
            'transfer_id' => $transferId,
            'order_no' => $orderNo,
            'amount' => $paymentTransaction->payment_amount
        ]);
    }

    /**
     * Update temp_order_payment status to success
     *
     * @param int $tempPreOrderId
     * @return void
     */
    protected function updateTempOrderPaymentSuccess(int $tempPreOrderId): void
    {
        DB::table('temp_order_payment')
            ->where('temp_preorder_id', $tempPreOrderId)
            ->update([
                'status' => 'success'
            ]);

        Log::info('Temp order payment updated to success', [
            'temp_pre_order_id' => $tempPreOrderId
        ]);
    }

    /**
     * Create actual order and order details from temp pre-order
     * Fixed: Create proper orders based on subscription days and meal items
     *
     * @param object $tempPreOrder
     * @param Request $request
     * @return array
     */
    protected function createActualOrdersFromTemp(object $tempPreOrder, Request $request): array
    {
        // Check if orders already exist for this order_no to prevent duplicates
        $existingOrders = DB::table('orders')->where('order_no', $tempPreOrder->order_no)->count();
        if ($existingOrders > 0) {
            Log::warning('Orders already exist for order_no, skipping creation', [
                'order_no' => $tempPreOrder->order_no,
                'existing_count' => $existingOrders
            ]);

            return DB::table('orders')
                ->where('order_no', $tempPreOrder->order_no)
                ->get(['pk_order_no as order_id', 'order_no', 'order_date'])
                ->map(function($order) {
                    return [
                        'order_id' => $order->order_id,
                        'order_no' => $order->order_no,
                        'order_date' => $order->order_date,
                        'status' => 'already_exists'
                    ];
                })->toArray();
        }

        // Parse order days from temp pre-order (handle both JSON array and simple string formats)
        $orderDays = [];

        if (is_string($tempPreOrder->order_days)) {
            // First try CSV format (new format: "2025-08-06,2025-08-13,2025-08-20")
            if (strpos($tempPreOrder->order_days, ',') !== false) {
                $orderDays = explode(',', $tempPreOrder->order_days);
                $orderDays = array_map('trim', $orderDays); // Remove any whitespace
            } else {
                // Try JSON format (legacy support)
                $decoded = json_decode($tempPreOrder->order_days, true);
                if (is_array($decoded)) {
                    $orderDays = $decoded;
                } else {
                    // Single date string
                    $orderDays = [$tempPreOrder->order_days];
                }
            }
        } else {
            $orderDays = [$tempPreOrder->order_days];
        }

        if (empty($orderDays)) {
            throw new Exception('Invalid order days data in temp pre-order');
        }

        Log::info('Parsed order days for payment success', [
            'raw_order_days' => $tempPreOrder->order_days,
            'parsed_order_days' => $orderDays,
            'count' => count($orderDays)
        ]);

        $createdOrders = [];

        // Create orders for each subscription day
        foreach ($orderDays as $orderDate) {
            // Check if order already exists for this date to prevent duplicates
            $existingOrder = DB::table('orders')
                ->where('order_no', $tempPreOrder->order_no)
                ->where('order_date', $orderDate)
                ->first(['pk_order_no']);

            if ($existingOrder) {
                Log::warning('Order already exists for this date, skipping', [
                    'order_no' => $tempPreOrder->order_no,
                    'order_date' => $orderDate,
                    'existing_order_id' => $existingOrder->pk_order_no
                ]);

                $createdOrders[] = [
                    'order_id' => $existingOrder->pk_order_no,
                    'order_no' => $tempPreOrder->order_no,
                    'order_date' => $orderDate,
                    'status' => 'already_exists'
                ];
                continue;
            }

            // Get meal items for this specific date from product_planner
            $mealItems = $this->getMealItemsForOrder($tempPreOrder, $orderDate);

            // Debug log for meal items
            Log::info('Meal items retrieved for order creation', [
                'order_date' => $orderDate,
                'meal_items_count' => count($mealItems),
                'meal_items' => $mealItems
            ]);

            // Create order for this specific date
            $orderId = $this->createSingleOrder($tempPreOrder, $tempPreOrder->order_no, $orderDate, $request);

            // Ensure we have at least one meal item (fallback to main product)
            if (empty($mealItems)) {
                Log::warning('No meal items found, creating fallback item', [
                    'order_date' => $orderDate,
                    'product_code' => $tempPreOrder->product_code
                ]);

                $mealItems = [
                    [
                        'product_code' => $tempPreOrder->product_code,
                        'product_name' => $tempPreOrder->product_name,
                        'quantity' => 1,
                        'amount' => $tempPreOrder->amount
                    ]
                ];
            }

            // Create order details for each meal item for this date
            $this->createOrderDetailsForOrder($orderId, $tempPreOrder->order_no, $tempPreOrder, $mealItems, $orderDate);

            $createdOrders[] = [
                'order_id' => $orderId,
                'order_no' => $tempPreOrder->order_no,
                'order_date' => $orderDate,
                'meal_items_count' => count($mealItems),
                'subscription_days' => count($orderDays),
                'order_days' => $orderDays,
                'status' => 'created'
            ];
        }

        Log::info('Created orders from temp pre-order', [
            'order_no' => $tempPreOrder->order_no,
            'total_orders_created' => count($createdOrders),
            'subscription_days' => count($orderDays),
            'meal_items_per_order' => count($mealItems)
        ]);

        return $createdOrders;
    }

    /**
     * Get meal items for the order from product_planner table (like weekly-planner API)
     * Fetches meal items based on product items and product_planner table
     *
     * @param object $tempPreOrder
     * @param string $orderDate The specific date for this order
     * @return array
     */
    protected function getMealItemsForOrder(object $tempPreOrder, string $orderDate = null): array
    {
        try {
            // Get the main product details to extract items
            $product = DB::table('products')
                ->where('pk_product_code', $tempPreOrder->product_code)
                ->where('status', 1)
                ->first(['pk_product_code', 'name', 'items', 'product_category']);

            Log::info('Product lookup for meal items extraction', [
                'product_code' => $tempPreOrder->product_code,
                'product_found' => $product ? 'yes' : 'no',
                'items_field' => $product->items ?? 'null',
                'items_empty' => empty($product->items ?? ''),
                'items_length' => strlen($product->items ?? ''),
                'product_name' => $product->name ?? 'null'
            ]);

            if (!$product || empty($product->items)) {
                Log::info('No product found or no items defined, using main product', [
                    'product_code' => $tempPreOrder->product_code,
                    'product_found' => $product ? 'yes' : 'no',
                    'items_field' => $product->items ?? 'null'
                ]);

                return [
                    [
                        'product_code' => $tempPreOrder->product_code,
                        'product_name' => $tempPreOrder->product_name,
                        'quantity' => 1,
                        'amount' => $tempPreOrder->amount / $tempPreOrder->quantity // Calculate per item amount
                    ]
                ];
            }

            // Parse items from product (JSON format like {"395":"1","396":"1","334":"1"})
            $items = [];
            $itemsData = json_decode($product->items, true);

            if (is_array($itemsData)) {
                // Handle JSON object format: {"395":"1","396":"1","334":"1"}
                foreach ($itemsData as $itemCode => $quantity) {
                    $items[] = [
                        'item_code' => (int)$itemCode,
                        'quantity' => (int)$quantity
                    ];
                }
                Log::info('Parsed JSON format items', [
                    'product_code' => $tempPreOrder->product_code,
                    'items_raw' => $product->items,
                    'parsed_items_count' => count($items),
                    'parsed_items' => $items
                ]);
            } else {
                // Handle old format like "343:1,344:1,345:1"
                $itemPairs = explode(',', $product->items);
                foreach ($itemPairs as $pair) {
                    if (strpos($pair, ':') !== false) {
                        list($itemCode, $quantity) = explode(':', $pair);
                        $items[] = ['item_code' => (int)$itemCode, 'quantity' => (int)$quantity];
                    }
                }
                Log::info('Parsed old format items', [
                    'product_code' => $tempPreOrder->product_code,
                    'items_raw' => $product->items,
                    'parsed_items_count' => count($items)
                ]);
            }

            if (empty($items)) {
                Log::info('No items found in product, using main product', [
                    'product_code' => $tempPreOrder->product_code,
                    'items_raw' => $product->items
                ]);

                return [
                    [
                        'product_code' => $tempPreOrder->product_code,
                        'product_name' => $tempPreOrder->product_name,
                        'quantity' => 1,
                        'amount' => $tempPreOrder->amount / $tempPreOrder->quantity // Calculate per item amount
                    ]
                ];
            }

            $mealItemsArray = [];
            $useDate = $orderDate ?? now()->format('Y-m-d');

            // For each item, check product_planner table for specific product name
            foreach ($items as $item) {
                $itemCode = $item['item_code'] ?? $item['product_code'] ?? 0;
                $quantity = $item['quantity'] ?? 1;

                if ($itemCode <= 0) continue;

                // Check product_planner table for specific product name (like weekly-planner API)
                $plannerItem = DB::table('product_planner')
                    ->where('date', $useDate)
                    ->where('menu', strtolower($tempPreOrder->order_menu))
                    ->where('fk_kitchen_code', (int)$tempPreOrder->fk_kitchen_code)
                    ->where('generic_product_code', $itemCode)
                    ->first(['specific_product_code', 'specific_product_name']);

                $itemName = '';
                $source = '';

                // Priority logic: planner data first, then fallback to products table
                if ($plannerItem && !empty($plannerItem->specific_product_name)) {
                    $itemName = $plannerItem->specific_product_name;
                    $source = 'product_planner_table';
                } else {
                    // Fallback to products table name
                    $productDetails = DB::table('products')
                        ->where('pk_product_code', $itemCode)
                        ->first(['name', 'unit_price']);

                    if ($productDetails) {
                        $itemName = $productDetails->name;
                        $source = 'products_table_fallback';
                    } else {
                        $itemName = 'Unknown Item';
                        $source = 'not_found';
                    }
                }

                // Calculate proportional amount for this item
                $itemAmount = count($items) > 0 ? ($tempPreOrder->amount / count($items)) : $tempPreOrder->amount;

                $mealItemsArray[] = [
                    'product_code' => $itemCode,
                    'product_name' => $itemName,
                    'quantity' => $quantity,
                    'amount' => $itemAmount,
                    'source' => $source,
                    'date' => $useDate
                ];
            }

            Log::info('Retrieved meal items from product_planner', [
                'product_code' => $tempPreOrder->product_code,
                'menu' => $tempPreOrder->order_menu,
                'date' => $useDate,
                'items_count' => count($mealItemsArray)
            ]);

            return $mealItemsArray;

        } catch (\Exception $e) {
            Log::error('Failed to get meal items from product_planner, using fallback', [
                'product_code' => $tempPreOrder->product_code,
                'error' => $e->getMessage()
            ]);

            // Fallback to single main product
            return [
                [
                    'product_code' => $tempPreOrder->product_code,
                    'product_name' => $tempPreOrder->product_name,
                    'quantity' => 1,
                    'amount' => $tempPreOrder->amount
                ]
            ];
        }
    }

    /**
     * Calculate tax amount based on settings table configuration
     *
     * @param float $amount
     * @return float
     */
    protected function calculateOrderTax(float $amount, ?int $companyId = null): float
    {
        try {
            // Get tax settings from settings table (company-scoped if available)
            $applyTax = $this->getSettingValueFlexible('GLOBAL_APPLY_TAX', 'no', $companyId);

            if ($applyTax !== 'yes') {
                return 0.00;
            }

            $taxMethod = (string) ($this->getSettingValueFlexible('GLOBAL_TAX_METHOD', 'exclusive', $companyId) ?? 'exclusive');

            // Get active tax rates
            $taxes = DB::table('tax')
                ->where('status', 1)
                ->where('apply_all_product', 'yes')
                ->get(['tax', 'tax_type', 'base_amount']);

            $totalTax = 0.00;

            foreach ($taxes as $tax) {
                if ($tax->tax_type === 'percent') {
                    $baseAmount = ($tax->base_amount / 100);
                    $taxableAmount = $amount * $baseAmount;

                    if ($taxMethod === 'exclusive') {
                        $totalTax += ($taxableAmount * $tax->tax) / 100;
                    } else { // inclusive
                        $totalTax += $taxableAmount / (1 + ($tax->tax / 100)) * ($tax->tax / 100);
                    }
                } elseif ($tax->tax_type === 'fixed') {
                    $totalTax += $tax->tax;
                }
            }

            return round($totalTax, 2);

        } catch (\Exception $e) {
            Log::error('Tax calculation failed', [
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return 0.00;
        }
    }

    /**
     * Generate daily order number
     *
     * @param string $baseOrderNo
     * @param string $orderDate
     * @return string
     */
    protected function generateDailyOrderNumber(string $baseOrderNo, string $orderDate): string
    {
        $dateStr = str_replace('-', '', $orderDate);
        return $baseOrderNo . '_' . $dateStr;
    }

    /**
     * Create single order record
     *
     * @param object $tempPreOrder
     * @param string $dailyOrderNo
     * @param string $orderDate
     * @param Request $request
     * @return int
     */
    protected function createSingleOrder(object $tempPreOrder, string $dailyOrderNo, string $orderDate, Request $request): int
    {
        // Calculate tax based on settings - ensure amount is float
        $companyId = (int)($request->input('company_id'));
        $taxAmount = $this->calculateOrderTax((float) $tempPreOrder->amount, $companyId);

        // Get meal items to create better product name and description
        $mealItems = $this->getMealItemsForOrder($tempPreOrder, $orderDate);

        // Get original product name directly from products table and create description based on meal items
        $productName = $this->getOriginalProductName($tempPreOrder->product_code, $tempPreOrder->product_name);
        $productDescription = $this->createMealItemsDescription($mealItems, $tempPreOrder);

        $orderId = DB::table('orders')->insertGetId([
            'company_id' => $tempPreOrder->company_id,
            'unit_id' => $tempPreOrder->unit_id,
            'fk_kitchen_code' => $tempPreOrder->fk_kitchen_code,
            'ref_order' => $tempPreOrder->pk_order_no, // Reference to temp pre-order
            'order_no' => $dailyOrderNo,
            'auth_id' => $tempPreOrder->auth_id,
            'customer_code' => $tempPreOrder->customer_code,
            'customer_name' => $tempPreOrder->customer_name,
            'food_preference' => $tempPreOrder->food_preference,
            'phone' => $tempPreOrder->phone,
            'email_address' => $tempPreOrder->email_address,
            'location_code' => $tempPreOrder->location_code,
            'location_name' => $tempPreOrder->location_name,
            'city' => $tempPreOrder->city,
            'city_name' => $tempPreOrder->city_name,
            'product_code' => $tempPreOrder->product_code,
            'product_name' => $productName,
            'product_description' => $productDescription,
            'product_type' => $tempPreOrder->product_type,
            'quantity' => $tempPreOrder->quantity,
            'product_price' => $tempPreOrder->product_price,
            'amount' => $tempPreOrder->amount,
            'applied_discount' => $tempPreOrder->applied_discount,
            'amount_paid' => 1, // Payment completed
            'tax' => $taxAmount, // Use calculated tax amount
            'delivery_charges' => $tempPreOrder->delivery_charges,
            'service_charges' => $tempPreOrder->service_charges,
            'order_status' => 'New', // Fixed: Should be 'New' not 'Confirmed'
            'order_date' => $orderDate,
            'due_date' => null, // Set to NULL as per requirement
            'ship_address' => $tempPreOrder->ship_address,
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'order_menu' => $tempPreOrder->order_menu,
            'inventory_type' => $tempPreOrder->inventory_type,
            'food_type' => $tempPreOrder->food_type,
            'payment_mode' => 'online', // Fixed: Always 'online' for online transactions
            'days_preference' => $tempPreOrder->days_preference,
            'delivery_type' => $tempPreOrder->delivery_type,
            'delivery_time' => $tempPreOrder->delivery_time,
            'delivery_end_time' => $tempPreOrder->delivery_end_time,
            'recurring_status' => '1',
            'created_date' => now(),
        ]);

        // Lock wallet amount for this order (for cancellation tracking)
        // Determine meal type from order_menu (breakfast, lunch, dinner)
        $mealType = strtolower($tempPreOrder->order_menu ?? 'lunch');
        // Cast amount to float to match method signature
        $this->lockWalletAmountForOrder($tempPreOrder->customer_code, $tempPreOrder->order_no, $orderDate, (float) $tempPreOrder->amount, $mealType);

        return $orderId;
    }

    /**
     * Create order details for a single order
     *
     * @param int $orderId
     * @param string $dailyOrderNo
     * @param object $tempPreOrder
     * @param array $mealItems
     * @param string $orderDate
     * @return void
     */
    protected function createOrderDetailsForOrder(int $orderId, string $dailyOrderNo, object $tempPreOrder, array $mealItems, string $orderDate): void
    {
        foreach ($mealItems as $item) {
            // Calculate tax for this specific item - ensure amount is float
            $itemTax = $this->calculateOrderTax((float) $item['amount'], (int)$tempPreOrder->company_id);

            DB::table('order_details')->insert([
                'company_id' => $tempPreOrder->company_id,
                'unit_id' => $tempPreOrder->unit_id,
                'ref_order_no' => $dailyOrderNo,
                'meal_code' => $tempPreOrder->product_code,
                'product_code' => $item['product_code'],
                'product_name' => $item['product_name'],
                'quantity' => $item['quantity'],
                'product_type' => 'Meal',
                'order_date' => $orderDate,
                'product_amount' => 0.00, // Set to 0 as requested
                'product_tax' => 0.00, // Set to 0 as requested
                'product_subtype' => 'specific',
                'product_generic_code' => $item['product_code'],
                'product_generic_name' => $item['product_name'],
            ]);
        }

        Log::info('Order details created', [
            'order_id' => $orderId,
            'order_no' => $dailyOrderNo,
            'order_date' => $orderDate,
            'meal_items_count' => count($mealItems)
        ]);
    }

    /**
     * Count total order details created
     *
     * @param array $createdOrders
     * @return int
     */
    protected function countOrderDetails(array $createdOrders): int
    {
        $totalDetails = 0;
        foreach ($createdOrders as $order) {
            // Use meal_items_count if available, otherwise count from order_details table
            if (isset($order['meal_items_count'])) {
                $totalDetails += $order['meal_items_count'];
            } else {
                // Fallback: count from database
                $count = DB::table('order_details')
                    ->where('ref_order_no', $order['order_no'] ?? '')
                    ->count();
                $totalDetails += $count;
            }
        }
        return $totalDetails;
    }

    /**
     * Process selected days from request - handle both new and legacy formats
     *
     * @param array $validated
     * @return array
     */
    protected function processSelectedDays(array $validated): array
    {
        // If new format is provided, use it
        if (isset($validated['selected_days']) && is_array($validated['selected_days'])) {
            return array_map('intval', $validated['selected_days']);
        }

        // Legacy format: convert days_preference string to array
        if (isset($validated['days_preference'])) {
            $daysString = trim($validated['days_preference']);
            if (!empty($daysString)) {
                return array_map('intval', explode(',', $daysString));
            }
        }

        // Default to Monday-Friday if nothing provided
        return [1, 2, 3, 4, 5]; // Monday to Friday
    }

    /**
     * Calculate delivery dates based on start date, selected days, and subscription days
     * Logic similar to admin service working days calculation with cut-off restrictions
     *
     * @param string $startDate Format: Y-m-d
     * @param array $selectedDays Array of day numbers (0=Sunday, 6=Saturday)
     * @param int $subscriptionDays Number of delivery days needed
     * @return array Array of delivery dates in Y-m-d format
     */
    protected function calculateDeliveryDates(string $startDate, array $selectedDays, int $subscriptionDays): array
    {
        $deliveryDates = [];
        $currentDate = \Carbon\Carbon::createFromFormat('Y-m-d', $startDate);
        $daysFound = 0;
        $maxIterations = 365; // Prevent infinite loop
        $today = \Carbon\Carbon::now();

        // Get cut-off settings for kitchen K1 and meal type (breakfast/lunch)
        //$cutOffSettings = $this->getCutOffSettings(1, 'breakfast'); // Default to breakfast for K1
        $iterations = 0;

        Log::info('Calculating delivery dates', [
            'start_date' => $startDate,
            'selected_days' => $selectedDays,
            'subscription_days' => $subscriptionDays
        ]);

        while ($daysFound < $subscriptionDays && $iterations < $maxIterations) {
            $dayOfWeek = $currentDate->dayOfWeek; // 0=Sunday, 1=Monday, ..., 6=Saturday

            // Check if current day is in selected days
            if (in_array($dayOfWeek, $selectedDays)) {
                // For subscription orders, use simplified availability check
                // Only exclude dates that are in the past (before today)
                if ($currentDate->format('Y-m-d') >= $today->format('Y-m-d')) {
                    $deliveryDates[] = $currentDate->format('Y-m-d');
                    $daysFound++;

                    Log::debug('Added delivery date', [
                        'date' => $currentDate->format('Y-m-d'),
                        'day_of_week' => $dayOfWeek,
                        'days_found' => $daysFound
                    ]);
                } else {
                    Log::debug('Date excluded (past date)', [
                        'date' => $currentDate->format('Y-m-d'),
                        'day_of_week' => $dayOfWeek
                    ]);
                }
            }

            $currentDate->addDay();
            $iterations++;
        }

        if ($iterations >= $maxIterations) {
            Log::warning('Reached maximum iterations while calculating delivery dates', [
                'start_date' => $startDate,
                'selected_days' => $selectedDays,
                'subscription_days' => $subscriptionDays,
                'days_found' => $daysFound
            ]);
        }

        Log::info('Delivery dates calculated', [
            'total_dates' => count($deliveryDates),
            'dates' => $deliveryDates
        ]);

        return $deliveryDates;
    }

    /**
     * Get cut-off settings for a specific kitchen and meal type
     * Based on admin-service-v12 logic
     *
     * @param int $kitchenId
     * @param string $mealType (breakfast or lunch)
     * @return array
     */
    protected function getCutOffSettings(int $companyId, int $kitchenId, string $mealType): array
    {
        $mealTypeUpper = strtoupper($mealType);

        // Build the setting keys based on kitchen ID and meal type
        $cutOffDayKey = "K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_DAY";
        $cutOffTimeKey = "K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_TIME";

        Log::info("Looking for cut-off settings", [
            'cut_off_day_key' => $cutOffDayKey,
            'cut_off_time_key' => $cutOffTimeKey
        ]);

        try {
            // Use flexible settings reader with company scoping
            $cutOffDayVal = $this->getSettingValueFlexible($cutOffDayKey, '0', $companyId);
            $cutOffTimeVal = $this->getSettingValueFlexible($cutOffTimeKey, '23:59:59', $companyId);

            $cutOffDay = (int) ($cutOffDayVal ?? '0');
            $cutOffTime = (string) ($cutOffTimeVal ?? '23:59:59');

            Log::info("Cut-off settings retrieved", [
                'cut_off_day' => $cutOffDay,
                'cut_off_time' => $cutOffTime
            ]);

            return [
                'cut_off_day' => $cutOffDay,
                'cut_off_time' => $cutOffTime
            ];
        } catch (\Exception $e) {
            Log::warning("Failed to get cut-off settings, using defaults", [
                'error' => $e->getMessage()
            ]);

            return [
                'cut_off_day' => 0,
                'cut_off_time' => '23:59:59'
            ];
        }
    }

    /**
     * Check if a date is available for ordering based on cut-off settings
     * Based on admin-service-v12 logic
     *
     * @param \Carbon\Carbon $targetDate
     * @param \Carbon\Carbon $currentDateTime
     * @param array $cutOffSettings
     * @return bool
     */
    protected function isDateAvailableForOrdering(\Carbon\Carbon $targetDate, \Carbon\Carbon $currentDateTime, array $cutOffSettings): bool
    {
        $targetDateOnly = $targetDate->copy()->startOfDay();
        $today = $currentDateTime->copy()->startOfDay();

        // For future dates (not today), they are available
        if ($targetDateOnly->gt($today)) {
            Log::debug("Date {$targetDate->format('Y-m-d')} is in the future, including");
            return true;
        }

        // For today's date, check cut-off logic
        $cutOffDay = $cutOffSettings['cut_off_day'];
        $cutOffTime = $cutOffSettings['cut_off_time'];

        // Calculate the actual cut-off datetime
        $cutOffDateTime = $currentDateTime->copy()->startOfDay();

        // Add cut-off days
        if ($cutOffDay > 0) {
            $cutOffDateTime->addDays($cutOffDay);
        }

        // Add cut-off time
        try {
            $timeParts = explode(':', $cutOffTime);
            $cutOffDateTime->setTime((int)$timeParts[0], (int)$timeParts[1], (int)($timeParts[2] ?? 0));
        } catch (\Exception $e) {
            Log::warning("Invalid cut-off time format, using end of day", [
                'cut_off_time' => $cutOffTime,
                'error' => $e->getMessage()
            ]);
            $cutOffDateTime->setTime(23, 59, 59);
        }

        // If cut-off day is 0 (same day), check if current time is before cut-off time
        if ($cutOffDay == 0) {
            $isAvailable = $currentDateTime->lt($cutOffDateTime);
            Log::debug("Same day cut-off check", [
                'target_date' => $targetDate->format('Y-m-d'),
                'current_time' => $currentDateTime->format('Y-m-d H:i:s'),
                'cut_off_time' => $cutOffDateTime->format('Y-m-d H:i:s'),
                'is_available' => $isAvailable
            ]);
            return $isAvailable;
        }

        // For cut-off day > 0, the date is available if we're before the cut-off datetime
        $isAvailable = $currentDateTime->lt($cutOffDateTime);
        Log::debug("Multi-day cut-off check", [
            'target_date' => $targetDate->format('Y-m-d'),
            'current_time' => $currentDateTime->format('Y-m-d H:i:s'),
            'cut_off_datetime' => $cutOffDateTime->format('Y-m-d H:i:s'),
            'cut_off_day' => $cutOffDay,
            'is_available' => $isAvailable
        ]);

        return $isAvailable;
    }

    /**
     * Get customer details from database
     *
     * @param int $customerId
     * @return array|null
     */
    protected function getCustomerDetails(int $customerId): ?array
    {
        try {
            $customer = DB::table('customers')
                ->where('pk_customer_code', $customerId)
                ->where('status', 1) // Only active customers
                ->first(['pk_customer_code', 'customer_name', 'email_address', 'phone']);

            if (!$customer) {
                Log::warning('Customer not found or inactive', ['customer_id' => $customerId]);
                return null;
            }

            return [
                'id' => $customer->pk_customer_code,
                'name' => $customer->customer_name,
                'email' => $customer->email_address,
                'phone' => $customer->phone
            ];
        } catch (\Exception $e) {
            Log::error('Failed to fetch customer details', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Process meals array and fetch product details
     *
     * @param array $meals
     * @return array
     */
    protected function processMeals(array $meals): array
    {
        $processedMeals = [];

        foreach ($meals as $meal) {
            try {
                // Fetch product details from database
                $product = DB::table('products')
                    ->where('pk_product_code', $meal['product_code'])
                    ->where('status', 1)
                    ->first(['pk_product_code', 'name', 'description', 'product_type', 'unit_price']);

                if (!$product) {
                    Log::warning('Product not found or inactive', [
                        'product_code' => $meal['product_code']
                    ]);
                    continue;
                }

                Log::info('Found product for meal', [
                    'product_code' => $meal['product_code'],
                    'product' => $product
                ]);

                $quantity = $meal['quantity'];
                $totalAmount = $product->unit_price * $quantity;

                $processedMeals[] = [
                    'product_code' => $product->pk_product_code ?? $meal['product_code'],
                    'product_name' => $product->name ?? 'Unknown Product',
                    'product_description' => $product->description ?? $product->name ?? 'Unknown Product',
                    'product_type' => $product->product_type ?? 'Meal',
                    'quantity' => $quantity,
                    'unit_price' => $product->unit_price ?? 0,
                    'total_amount' => $totalAmount
                ];

                Log::info('Processed meal', [
                    'product_code' => $product->pk_product_code,
                    'product_name' => $product->name,
                    'quantity' => $quantity,
                    'total_amount' => $totalAmount
                ]);

            } catch (\Exception $e) {
                Log::error('Failed to process meal', [
                    'meal' => $meal,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Processed meals summary', [
            'total_meals' => count($processedMeals),
            'total_amount' => array_sum(array_column($processedMeals, 'total_amount'))
        ]);

        return $processedMeals;
    }

    /**
     * Get tax settings from database
     *
     * @return array
     */
    protected function getTaxSettings(): array
    {
        try {
            $globalApplyTax = DB::table('settings')
                ->where('key', 'GLOBAL_APPLY_TAX')
                ->first(['value']);

            $applyTax = $globalApplyTax && strtolower($globalApplyTax->value) === 'yes';
            $taxRate = 5.0; // Default 5% tax rate

            Log::info('Tax settings retrieved', [
                'apply_tax' => $applyTax,
                'tax_rate' => $taxRate
            ]);

            return [
                'apply' => $applyTax,
                'rate' => $taxRate
            ];
        } catch (\Exception $e) {
            Log::warning('Failed to get tax settings, using defaults', [
                'error' => $e->getMessage()
            ]);

            return [
                'apply' => false,
                'rate' => 5.0
            ];
        }
    }

    /**
     * Group meals by meal type (breakfast, lunch, etc.)
     * Uses product name to determine meal type since product_type is generic "Meal"
     *
     * @param array $processedMeals
     * @return array
     */
    protected function groupMealsByType(array $processedMeals): array
    {
        $mealsByType = [];

        foreach ($processedMeals as $meal) {
            $productName = strtolower($meal['product_name'] ?? '');
            $productDescription = strtolower($meal['product_description'] ?? '');

            // Determine meal type from product name or description
            $mealType = 'meal'; // Default

            if (strpos($productName, 'breakfast') !== false || strpos($productDescription, 'breakfast') !== false) {
                $mealType = 'breakfast';
            } elseif (strpos($productName, 'lunch') !== false || strpos($productDescription, 'lunch') !== false) {
                $mealType = 'lunch';
            } elseif (strpos($productName, 'dinner') !== false || strpos($productDescription, 'dinner') !== false) {
                $mealType = 'dinner';
            } elseif (strpos($productName, 'snack') !== false || strpos($productDescription, 'snack') !== false) {
                $mealType = 'snack';
            }

            // If still generic, try to use product_code ranges (common pattern)
            if ($mealType === 'meal') {
                $productCode = $meal['product_code'];
                if ($productCode >= 340 && $productCode <= 349) {
                    $mealType = 'breakfast';
                } elseif ($productCode >= 330 && $productCode <= 339) {
                    $mealType = 'lunch';
                } elseif ($productCode >= 350 && $productCode <= 359) {
                    $mealType = 'dinner';
                }
            }

            if (!isset($mealsByType[$mealType])) {
                $mealsByType[$mealType] = [];
            }

            $mealsByType[$mealType][] = $meal;

            Log::info('Classified meal type', [
                'product_code' => $meal['product_code'],
                'product_name' => $meal['product_name'],
                'classified_as' => $mealType
            ]);
        }

        Log::info('Grouped meals by type', [
            'meal_types' => array_keys($mealsByType),
            'counts' => array_map('count', $mealsByType)
        ]);

        return $mealsByType;
    }

    /**
     * Create item preference JSON like the example
     *
     * @param array $meals
     * @return string
     */
    protected function createItemPreferenceJson(array $meals): string
    {
        $itemPreference = [];

        foreach ($meals as $meal) {
            $itemPreference[$meal['product_code']] = [
                'name' => $meal['product_name'],
                'product_name' => $meal['product_name'],
                'description' => $meal['product_description'] ?? $meal['product_name'],
                'unit_price' => number_format((float) $meal['unit_price'], 2),
                'id' => (string) $meal['product_code'],
                'foodtype' => 'veg', // Default, could be enhanced
                'product_category' => 'General', // Default, could be enhanced
                'image_path' => '',
                'product_subtype' => 'generic',
                'product_code' => (string) $meal['product_code'],
                'swap_with' => 'nocharge',
                'swap_charges' => null,
                'quantity' => (string) $meal['quantity']
            ];
        }

        return json_encode($itemPreference);
    }

    /**
     * Get actual product name from meals (use first meal's name, or fallback to generic)
     *
     * @param array $meals
     * @param string $mealType
     * @return string
     */
    protected function getActualProductName(array $meals, string $mealType): string
    {
        // If we have meals, use the first meal's product name
        if (!empty($meals) && isset($meals[0]['product_name'])) {
            return $meals[0]['product_name'];
        }

        // Fallback to generic display names
        $displayNames = [
            'breakfast' => 'Breakfast of the Day (Recommended)',
            'lunch' => 'Lunch of the Day (Recommended)',
            'dinner' => 'Dinner of the Day (Recommended)',
            'meal' => 'Meal of the Day (Recommended)'
        ];

        return $displayNames[$mealType] ?? ucfirst($mealType) . ' Subscription';
    }

    /**
     * Get actual product description from meals (use first meal's description, or fallback to generic)
     *
     * @param array $meals
     * @param string $mealType
     * @return string
     */
    protected function getActualProductDescription(array $meals, string $mealType): string
    {
        // If we have meals, use the first meal's product description
        if (!empty($meals) && isset($meals[0]['product_description'])) {
            return $meals[0]['product_description'];
        }

        // Fallback to generic descriptions
        return $this->getMealTypeDescription($mealType);
    }

    /**
     * Get display name for meal type (kept for backward compatibility)
     *
     * @param string $mealType
     * @return string
     */
    protected function getMealTypeDisplayName(string $mealType): string
    {
        $displayNames = [
            'breakfast' => 'Breakfast of the Day (Recommended)',
            'lunch' => 'Lunch of the Day (Recommended)',
            'dinner' => 'Dinner of the Day (Recommended)',
            'meal' => 'Meal of the Day (Recommended)'
        ];

        return $displayNames[$mealType] ?? ucfirst($mealType) . ' Subscription';
    }

    /**
     * Get description for meal type
     *
     * @param string $mealType
     * @return string
     */
    protected function getMealTypeDescription(string $mealType): string
    {
        $descriptions = [
            'breakfast' => 'A rotating menu of Indian and International breakfast options to introduce diverse flavours',
            'lunch' => 'A rotating menu of Indian and International lunch options to introduce diverse flavours',
            'dinner' => 'A rotating menu of Indian and International dinner options to introduce diverse flavours',
            'meal' => 'A rotating menu of Indian and International options to introduce diverse flavours'
        ];

        return $descriptions[$mealType] ?? 'A variety of meal options';
    }

    /**
     * Group orders by order_id and categorize them by status
     *
     * @param \Illuminate\Support\Collection $orders
     * @return array
     */
    protected function groupAndCategorizeOrders($orders): array
    {
        $groupedOrders = [];
        $categorizedOrders = [
            'upcoming' => [],
            'cancelled' => [],
            'other' => [],
            'all' => []
        ];

        // Group orders by order_id first
        foreach ($orders as $order) {
            $orderId = $order->order_id;

            if (!isset($groupedOrders[$orderId])) {
                $groupedOrders[$orderId] = [
                    'order_id' => $order->order_id,
                    'order_no' => $order->order_no,
                    'order_date' => $order->order_date,
                    'delivery_date' => $order->delivery_date,
                    'order_status' => $order->order_status,
                    'delivery_status' => $order->delivery_status,
                    'payment_mode' => $order->payment_mode,
                    'amount_paid' => $order->amount_paid,
                    'total_amount' => $order->total_amount,
                    'delivery_time' => $order->delivery_time,
                    'delivery_end_time' => $order->delivery_end_time,
                    'recurring_status' => $order->recurring_status,
                    'days_preference' => $order->days_preference,
                    'customer_address' => $order->customer_address,
                    'student_name' => $this->extractStudentName($order->customer_address),
                    'location_name' => $order->location_name,
                    'city_name' => $order->city_name,
                    'food_preference' => $order->food_preference,
                    'product_code' => $order->product_code,
                    'product_name' => $order->product_name,
                    'image_path'   => $this->assetUrl($order->image_path,$order->company_id),
                    'product_type' => $this->getEnhancedProductType($order->product_name, $order->product_code),
                    'original_product_type' => $order->product_type, // Keep original for reference
                    'quantity' => $order->quantity,
                    'item_amount' => $order->item_amount,
                    'last_modified' => $order->last_modified, // Include last_modified for cancelled_on
                    'meal_items' => []
                ];
            }

            // We'll fetch meal items from order_details table later
            // This is just placeholder for now
        }

        // Convert to array and categorize
        $allOrders = array_values($groupedOrders);
        $today = now()->format('Y-m-d');

        // Add plan_type, meal_items, and cancellable flag to each order
        foreach ($allOrders as &$order) {
            $order['plan_type'] = $this->determinePlanType($order['order_no']);
            $order['meal_items'] = $this->getMealItemsFromOrderDetails($order['order_no'], $order['order_date']);

            // Add cancellable flag with time-based policy
            $order = $this->addCancellableFlag($order);

            $categorizedOrders['all'][] = $order;

            // Categorization logic
            $orderStatus = strtolower($order['order_status'] ?? '');
            $deliveryStatus = strtolower($order['delivery_status'] ?? '');
            $deliveryDate = $order['delivery_date'];

            // Cancelled orders (check this FIRST, regardless of date)
            if (in_array($orderStatus, ['cancelled', 'canceled', 'cancel', 'refunded']) ||
                in_array($deliveryStatus, ['cancelled', 'canceled', 'cancel'])) {
                // Add cancelled_on timestamp using last_modified value
                $order['cancelled_on'] = $order['last_modified'];
                $categorizedOrders['cancelled'][] = $order;
            }
            // Upcoming orders (future delivery date and not cancelled/completed)
            elseif ($deliveryDate && $deliveryDate > $today &&
                    !in_array($orderStatus, ['completed', 'delivered', 'complete']) &&
                    !in_array($deliveryStatus, ['delivered', 'completed'])) {
                $categorizedOrders['upcoming'][] = $order;
            }
            // Other orders (past orders, completed, etc.)
            else {
                $categorizedOrders['other'][] = $order;
            }
        }

        // Sort each category by delivery_date (upcoming: ascending, others: descending)
        usort($categorizedOrders['upcoming'], function($a, $b) {
            return strcmp($a['delivery_date'], $b['delivery_date']);
        });

        usort($categorizedOrders['cancelled'], function($a, $b) {
            return strcmp($b['delivery_date'], $a['delivery_date']);
        });

        usort($categorizedOrders['other'], function($a, $b) {
            return strcmp($b['delivery_date'], $a['delivery_date']);
        });

        Log::info('Orders categorized successfully', [
            'total_orders' => count($allOrders),
            'upcoming_count' => count($categorizedOrders['upcoming']),
            'cancelled_count' => count($categorizedOrders['cancelled']),
            'other_count' => count($categorizedOrders['other'])
        ]);

        return $categorizedOrders;
    }

    private function assetUrl($imagePath, $companyId){
        if(empty($imagePath) || empty($companyId)){
            return null;
        }
        
        $region = config('filesystems.disks.s3.region');
        $bucket = config('filesystems.disks.s3.bucket');

        return 'https://s3.'.$region.'.amazonaws.com/'.$bucket.'/'.$companyId.'/product/'.$imagePath;
    }

    /**
     * Extract unique student names from customer addresses for each order category
     *
     * @param array $categorizedOrders
     * @return array
     */
    protected function extractStudentNames(array $categorizedOrders): array
    {
        $studentNames = [
            'upcoming' => [],
            'cancelled' => [],
            'other' => []
        ];

        foreach ($categorizedOrders as $category => $orders) {
            if ($category === 'all') {
                continue; // Skip the 'all' category
            }

            $uniqueNames = [];

            foreach ($orders as $order) {
                $customerAddress = $order['customer_address'] ?? '';

                // Extract student name (0th index after explode by comma)
                $addressParts = explode(',', $customerAddress);
                $studentName = trim($addressParts[0] ?? '');

                // Only add non-empty, unique student names
                if (!empty($studentName) && !in_array($studentName, $uniqueNames)) {
                    $uniqueNames[] = $studentName;
                }
            }

            // Sort student names alphabetically
            sort($uniqueNames);
            $studentNames[$category] = $uniqueNames;
        }

        Log::info('Student names extracted successfully', [
            'upcoming_students' => count($studentNames['upcoming']),
            'cancelled_students' => count($studentNames['cancelled']),
            'other_students' => count($studentNames['other']),
            'sample_upcoming' => array_slice($studentNames['upcoming'], 0, 3),
            'sample_cancelled' => array_slice($studentNames['cancelled'], 0, 3),
            'sample_other' => array_slice($studentNames['other'], 0, 3)
        ]);

        return $studentNames;
    }

    /**
     * Cancel an order with time-based refund processing
     *
     * @param Request $request
     * @param string $orderNo
     * @return JsonResponse
     */
    public function cancelOrder(Request $request, string $orderNo): JsonResponse
    {
        try {
            // Validate request - cancel_dates is required since one order_no can span multiple days
            $validated = $request->validate([
                'reason' => 'required|string|max:500',
                'cancel_dates' => 'required|array|min:1',
                'cancel_dates.*' => 'date|after_or_equal:today',
                'meal_type' => 'nullable|string|in:breakfast,lunch,dinner', // Optional meal type filter
                'request_timestamp' => 'nullable|date_format:Y-m-d H:i:s', // Optional client timestamp for validation
            ]);

            // Capture request processing time for validation
            $requestTime = isset($validated['request_timestamp'])
                ? \Carbon\Carbon::parse($validated['request_timestamp'])
                : now();

            $processingTime = now();
            $timeDifference = $processingTime->diffInSeconds($requestTime);

            // Log timing information
            Log::info('Cancellation request timing', [
                'order_no' => $orderNo,
                'request_time' => $requestTime->format('Y-m-d H:i:s'),
                'processing_time' => $processingTime->format('Y-m-d H:i:s'),
                'time_difference_seconds' => $timeDifference,
                'client_provided_timestamp' => isset($validated['request_timestamp'])
            ]);

            Log::info('Time-based order cancellation initiated', [
                'order_no' => $orderNo,
                'reason' => $validated['reason'],
                'cancel_dates' => $validated['cancel_dates'],
                'meal_type' => $validated['meal_type'] ?? 'all_meals'
            ]);

            // Start database transaction
            DB::beginTransaction();

            // Get orders to cancel for specific dates (cancel_dates is required)
            $ordersQuery = DB::table('orders')
                ->where('order_no', $orderNo)
                ->whereIn('order_date', $validated['cancel_dates'])
                ->whereIn('order_status', ['New', 'Confirmed', 'Processing']);

            // If specific meal type provided, filter by meal type
            if (!empty($validated['meal_type'])) {
                $ordersQuery->where('product_name', 'LIKE', '%' . $validated['meal_type'] . '%');
            }

            $ordersToCancel = $ordersQuery->get();

            if ($ordersToCancel->isEmpty()) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'No eligible orders found for cancellation. Orders can only be cancelled for current or future dates.'
                ], 404);
            }

            // Check if any orders are already prepared
            $preparedOrders = DB::table('orders')
                ->where('order_no', $orderNo)
                ->whereIn('order_status', ['Prepared', 'Dispatched', 'Delivered'])
                ->whereIn('order_date', $ordersToCancel->pluck('order_date')->toArray())
                ->exists();

            if ($preparedOrders) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot cancel orders that are already prepared, dispatched, or delivered.'
                ], 400);
            }

            // Validate timing for edge cases (e.g., request at 07:59:59 but processed at 08:00:01)
            $timingValidation = $this->validateCancellationTiming($ordersToCancel, $requestTime, $processingTime);
            if (!$timingValidation['is_valid']) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => $timingValidation['error_message'],
                    'error_code' => 'TIMING_VALIDATION_FAILED',
                    'details' => $timingValidation['details']
                ], 400);
            }

            // Process time-based cancellation with refund policies
            $cancellationResult = $this->processTimeBasedCancellation($ordersToCancel, $validated['reason'], $requestTime);

            DB::commit();

            Log::info('Time-based order cancellation completed successfully', [
                'order_no' => $orderNo,
                'cancelled_orders' => $cancellationResult['cancelled_orders'],
                'total_refund_amount' => $cancellationResult['total_refund_amount'],
                'wallet_credited' => $cancellationResult['wallet_credited'],
                'wallet_unlocked' => $cancellationResult['wallet_unlocked'],
                'refund_breakdown' => $cancellationResult['refund_breakdown']
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Orders cancelled successfully with time-based refund policy',
                'data' => $cancellationResult
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Order cancellation failed', [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Order cancellation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create descriptive product description based on meal items
     *
     * @param array $mealItems
     * @param object $tempPreOrder
     * @return string
     */
    protected function createMealItemsDescription(array $mealItems, object $tempPreOrder): string
    {
        if (empty($mealItems)) {
            return $tempPreOrder->product_description ?? 'Meal package';
        }

        // Create description from meal item names
        $itemNames = [];
        foreach ($mealItems as $item) {
            if (!empty($item['product_name'])) {
                $itemNames[] = $item['product_name'];
            }
        }

        if (empty($itemNames)) {
            return $tempPreOrder->product_description ?? 'Meal package';
        }

        // Create a descriptive text
        $description = 'Includes: ' . implode(', ', $itemNames);

        // Add meal type context if available
        $mealType = $tempPreOrder->order_menu ?? '';
        if (!empty($mealType)) {
            $description = ucfirst($mealType) . ' meal - ' . $description;
        }

        Log::info('Created meal items description', [
            'product_code' => $tempPreOrder->product_code,
            'meal_items_count' => count($mealItems),
            'item_names' => $itemNames,
            'description' => $description
        ]);

        return $description;
    }

    /**
     * Get original product name from products table
     *
     * @param int $productCode
     * @param string $fallbackName
     * @return string
     */
    protected function getOriginalProductName(int $productCode, string $fallbackName): string
    {
        try {
            $product = DB::table('products')
                ->where('pk_product_code', $productCode)
                ->where('status', 1)
                ->first(['name']);

            if ($product && !empty($product->name)) {
                Log::info('Retrieved original product name from products table', [
                    'product_code' => $productCode,
                    'original_name' => $product->name,
                    'fallback_name' => $fallbackName
                ]);
                return $product->name;
            }

            Log::warning('Product not found in products table, using fallback name', [
                'product_code' => $productCode,
                'fallback_name' => $fallbackName
            ]);

            return $fallbackName;

        } catch (\Exception $e) {
            Log::error('Failed to get original product name, using fallback', [
                'product_code' => $productCode,
                'fallback_name' => $fallbackName,
                'error' => $e->getMessage()
            ]);

            return $fallbackName;
        }
    }

    /**
     * Process time-based order cancellation with refund policies
     *
     * @param \Illuminate\Support\Collection $ordersToCancel
     * @param string $reason
     * @param \Carbon\Carbon $requestTime
     * @return array
     */
    protected function processTimeBasedCancellation($ordersToCancel, string $reason, \Carbon\Carbon $requestTime): array
    {
        $totalRefundAmount = 0;
        $totalWalletUnlocked = 0;
        $cancelledOrderIds = [];
        $customerCode = null;
        $orderNo = null;
        $refundBreakdown = [];

        foreach ($ordersToCancel as $order) {
            // Store customer and order info
            $customerCode = $order->customer_code;
            $orderNo = $order->order_no;

            // Determine meal type from product name
            $mealType = $this->determineMealTypeFromProduct($order->product_name);

            // Check cancellation eligibility and calculate refund using request timestamp
            $cancellationPolicy = $this->getCancellationPolicy($order, $mealType, $requestTime);

            if (!$cancellationPolicy['is_cancellable']) {
                Log::warning('Order not cancellable due to time restrictions', [
                    'order_id' => $order->pk_order_no,
                    'order_date' => $order->order_date,
                    'meal_type' => $mealType,
                    'current_time' => now()->format('H:i:s'),
                    'cutoff_time' => $cancellationPolicy['cutoff_time']
                ]);
                continue; // Skip this order
            }

            // Calculate base refund amount
            $baseRefundAmount = ($order->amount ?? 0) + ($order->tax ?? 0) +
                               ($order->delivery_charges ?? 0) + ($order->service_charges ?? 0) -
                               ($order->applied_discount ?? 0);

            // Apply refund percentage based on time policy
            $refundAmount = $baseRefundAmount * ($cancellationPolicy['refund_percentage'] / 100);
            $totalRefundAmount += $refundAmount;

            // Find and unlock wallet amount for this order
            $walletUnlocked = $this->unlockWalletAmount($customerCode, $order->order_no, $order->order_date, $mealType);
            $totalWalletUnlocked += $walletUnlocked;

            // Update order status to cancelled
            DB::table('orders')
                ->where('pk_order_no', $order->pk_order_no)
                ->update([
                    'order_status' => 'Cancelled',
                    'remark' => $reason . ' | Refund: ' . $cancellationPolicy['refund_percentage'] . '% | Policy: ' . $cancellationPolicy['policy_type'],
                    'last_modified' => now()
                ]);

            // Note: order_details table doesn't have a status column
            // The cancellation status is tracked in the orders table
            // Log the order details that would be affected
            Log::info('Order details affected by cancellation', [
                'order_no' => $order->order_no,
                'order_date' => $order->order_date,
                'order_id' => $order->pk_order_no
            ]);

            $cancelledOrderIds[] = $order->pk_order_no;

            // Store refund breakdown
            $refundBreakdown[] = [
                'order_id' => $order->pk_order_no,
                'order_date' => $order->order_date,
                'meal_type' => $mealType,
                'base_amount' => $baseRefundAmount,
                'refund_percentage' => $cancellationPolicy['refund_percentage'],
                'refund_amount' => $refundAmount,
                'wallet_unlocked' => $walletUnlocked,
                'policy_type' => $cancellationPolicy['policy_type'],
                'cutoff_time' => $cancellationPolicy['cutoff_time']
            ];

            Log::info('Order cancelled with time-based policy', [
                'order_id' => $order->pk_order_no,
                'order_no' => $order->order_no,
                'order_date' => $order->order_date,
                'meal_type' => $mealType,
                'base_amount' => $baseRefundAmount,
                'refund_percentage' => $cancellationPolicy['refund_percentage'],
                'refund_amount' => $refundAmount,
                'wallet_unlocked' => $walletUnlocked,
                'policy_type' => $cancellationPolicy['policy_type']
            ]);
        }

        // Process refund to wallet if any amount to refund
        $walletCredited = false;
        if ($totalRefundAmount > 0 && $customerCode) {
            $walletCredited = $this->processRefundToWallet($customerCode, $totalRefundAmount, $orderNo, $reason);
        }

        // Update kitchen data if needed
        $this->updateKitchenDataForCancellation($ordersToCancel);

        return [
            'cancelled_orders' => count($cancelledOrderIds),
            'cancelled_order_ids' => $cancelledOrderIds,
            'total_refund_amount' => $totalRefundAmount,
            'wallet_credited' => $walletCredited,
            'wallet_unlocked' => $totalWalletUnlocked,
            'customer_code' => $customerCode,
            'order_no' => $orderNo,
            'refund_breakdown' => $refundBreakdown
        ];
    }

    /**
     * Process order cancellation with refund and wallet update (Legacy method)
     *
     * @param \Illuminate\Support\Collection $ordersToCancel
     * @param string $reason
     * @return array
     */
    protected function processCancellation($ordersToCancel, string $reason): array
    {
        $totalRefundAmount = 0;
        $cancelledOrderIds = [];
        $customerCode = null;
        $orderNo = null;

        foreach ($ordersToCancel as $order) {
            // Store customer and order info
            $customerCode = $order->customer_code;
            $orderNo = $order->order_no;

            // Calculate refund amount (order amount + tax + delivery charges - discount)
            $refundAmount = ($order->amount ?? 0) + ($order->tax ?? 0) +
                           ($order->delivery_charges ?? 0) + ($order->service_charges ?? 0) -
                           ($order->applied_discount ?? 0);

            $totalRefundAmount += $refundAmount;

            // Update order status to cancelled
            DB::table('orders')
                ->where('pk_order_no', $order->pk_order_no)
                ->update([
                    'order_status' => 'Cancelled',
                    'remark' => $reason,
                    'last_modified' => now()
                ]);

            // Note: order_details table doesn't have a status column
            // The cancellation status is tracked in the orders table
            // Log the order details that would be affected
            Log::info('Order details affected by cancellation', [
                'order_no' => $order->order_no,
                'order_date' => $order->order_date,
                'order_id' => $order->pk_order_no
            ]);

            $cancelledOrderIds[] = $order->pk_order_no;

            Log::info('Order cancelled', [
                'order_id' => $order->pk_order_no,
                'order_no' => $order->order_no,
                'order_date' => $order->order_date,
                'refund_amount' => $refundAmount
            ]);
        }

        // Process refund and wallet credit
        $walletCredited = false;
        if ($totalRefundAmount > 0 && $customerCode) {
            $walletCredited = $this->processRefundToWallet($customerCode, $totalRefundAmount, $orderNo, $reason);
        }

        // Update kitchen data if needed
        $this->updateKitchenDataForCancellation($ordersToCancel);

        return [
            'cancelled_orders' => count($cancelledOrderIds),
            'cancelled_order_ids' => $cancelledOrderIds,
            'refund_amount' => $totalRefundAmount,
            'wallet_credited' => $walletCredited,
            'customer_code' => $customerCode,
            'order_no' => $orderNo
        ];
    }

    /**
     * Process refund to customer wallet (direct database insertion)
     *
     * @param int $customerCode
     * @param float $refundAmount
     * @param string $orderNo
     * @param string $reason
     * @return bool
     */
    protected function processRefundToWallet(int $customerCode, float $refundAmount, string $orderNo, string $reason): bool
    {
        try {
            // Derive company and unit from the order
            $order = DB::table('orders')->where('order_no', $orderNo)->first(['company_id', 'unit_id']);
            $companyId = (int)($order->company_id ?? 0);
            $unitId = (int)($order->unit_id ?? 0);
            // Since both services use the same database, insert directly into customer_wallet table
            $walletId = DB::table('customer_wallet')->insertGetId([
                'company_id' => $companyId,
                'unit_id' => $unitId,
                'fk_customer_code' => $customerCode,
                'wallet_amount' => $refundAmount,
                'amount_type' => 'cr', // Credit to wallet
                'reference_no' => 'REFUND_' . $orderNo . '_' . time(),
                'payment_date' => now()->toDateString(),
                'description' => "Refund of Rs. {$refundAmount} for cancelled order {$orderNo}. Reason: {$reason}",
                'created_by' => (int) (request()->input('user_id') ?? 0),
                'updated_by' => (int) (request()->input('user_id') ?? 0),
                'context' => 'admin', // Use valid enum value
                'payment_type' => 'online', // Use valid enum value (closest to refund)
                'created_date' => now(),
                'updated_date' => now()
            ]);

            Log::info('Refund credited to wallet (direct DB)', [
                'wallet_id' => $walletId,
                'customer_code' => $customerCode,
                'refund_amount' => $refundAmount,
                'order_no' => $orderNo
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to process refund to wallet', [
                'customer_code' => $customerCode,
                'refund_amount' => $refundAmount,
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }



    /**
     * Update kitchen data for cancelled orders
     *
     * @param \Illuminate\Support\Collection $ordersToCancel
     * @return void
     */
    protected function updateKitchenDataForCancellation($ordersToCancel): void
    {
        try {
            // Check if kitchen_data table exists and has the required columns
            $tableExists = DB::getSchemaBuilder()->hasTable('kitchen_data');

            if (!$tableExists) {
                Log::info('Kitchen data table does not exist, skipping kitchen data update', [
                    'orders_count' => $ordersToCancel->count()
                ]);
                return;
            }

            $hasStatusColumn = DB::getSchemaBuilder()->hasColumn('kitchen_data', 'status');

            if (!$hasStatusColumn) {
                Log::info('Kitchen data table does not have status column, skipping status update', [
                    'orders_count' => $ordersToCancel->count()
                ]);
                return;
            }

            foreach ($ordersToCancel as $order) {
                // Update kitchen production data
                DB::table('kitchen_data')
                    ->where('order_no', $order->order_no)
                    ->where('order_date', $order->order_date)
                    ->update([
                        'status' => 'Cancelled',
                        'updated_at' => now()
                    ]);
            }

            Log::info('Kitchen data updated for cancelled orders', [
                'orders_count' => $ordersToCancel->count()
            ]);

        } catch (\Exception $e) {
            Log::warning('Failed to update kitchen data for cancellation', [
                'error' => $e->getMessage(),
                'orders_count' => $ordersToCancel->count()
            ]);
        }
    }

    /**
     * Notify customer service of refund
     *
     * @param int $customerCode
     * @param float $refundAmount
     * @param string $orderNo
     * @return void
     */
    protected function notifyCustomerServiceOfRefund(int $customerCode, float $refundAmount, string $orderNo): void
    {
        try {
            // This could be enhanced to call customer service API
            // For now, we'll just log the notification
            Log::info('Customer service notified of refund', [
                'customer_code' => $customerCode,
                'refund_amount' => $refundAmount,
                'order_no' => $orderNo,
                'notification_sent' => true
            ]);

        } catch (\Exception $e) {
            Log::warning('Failed to notify customer service of refund', [
                'customer_code' => $customerCode,
                'refund_amount' => $refundAmount,
                'order_no' => $orderNo,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get city name by city ID from database
     *
     * @param int $cityId
     * @return string
     */
    protected function getCityNameById(int $cityId): string
    {
        try {
            // Handle specific known city mappings first
            $knownCities = [
                2 => 'Mumbai',
                9 => 'Mumbai', // Alternative ID for Mumbai
                1 => 'Delhi',
                3 => 'Bangalore',
                4 => 'Chennai',
                5 => 'Kolkata',
                6 => 'Hyderabad',
                7 => 'Pune',
                8 => 'Ahmedabad'
            ];

            if (isset($knownCities[$cityId])) {
                Log::info('City name from known mapping', [
                    'city_id' => $cityId,
                    'city_name' => $knownCities[$cityId]
                ]);
                return $knownCities[$cityId];
            }

            // Try database lookup with primary table structure
            $cityName = DB::table('cities')
                ->where('pk_city_code', $cityId)
                ->value('city_name');

            if ($cityName) {
                Log::info('City name fetched from database (pk_city_code)', [
                    'city_id' => $cityId,
                    'city_name' => $cityName
                ]);
                return $cityName;
            }

            // Fallback: try with different column names
            $cityName = DB::table('cities')
                ->where('id', $cityId)
                ->value('name');

            if ($cityName) {
                Log::info('City name fetched from database (id)', [
                    'city_id' => $cityId,
                    'city_name' => $cityName
                ]);
                return $cityName;
            }

            Log::warning('City name not found for ID', ['city_id' => $cityId]);
            return 'Unknown City';

        } catch (\Exception $e) {
            Log::error('Failed to fetch city name', [
                'city_id' => $cityId,
                'error' => $e->getMessage()
            ]);
            return 'Unknown City';
        }
    }

    /**
     * Get meal items from order_details table as CSV string
     *
     * @param string $orderNo
     * @param string $orderDate
     * @return string
     */
    protected function getMealItemsFromOrderDetails(string $orderNo, string $orderDate): string
    {
        try {
            // Fetch meal items from order_details table
            $mealItems = DB::table('order_details')
                ->where('ref_order_no', $orderNo)
                ->where('order_date', $orderDate)
                ->orderBy('product_name')
                ->pluck('product_name')
                ->toArray();

            // Remove duplicates and create CSV string
            $uniqueMealItems = array_unique($mealItems);
            $csvMealItems = implode(', ', $uniqueMealItems);

            Log::info('Fetched meal items from order_details', [
                'order_no' => $orderNo,
                'order_date' => $orderDate,
                'meal_items_count' => count($uniqueMealItems),
                'meal_items_csv' => $csvMealItems
            ]);

            return $csvMealItems;

        } catch (\Exception $e) {
            Log::warning('Failed to fetch meal items from order_details', [
                'order_no' => $orderNo,
                'order_date' => $orderDate,
                'error' => $e->getMessage()
            ]);

            return 'Meal items not available';
        }
    }

    /**
     * Determine plan type based on unique order count for the same order_no
     *
     * @param string $orderNo
     * @return string
     */
    protected function determinePlanType(string $orderNo): string
    {
        try {
            // Count unique orders with the same order_no
            $orderCount = DB::table('orders')
                ->where('order_no', $orderNo)
                ->count();

            // Determine plan type based on count
            if ($orderCount == 1) {
                return 'single day';
            } elseif ($orderCount >= 2 && $orderCount <= 7) {
                return $orderCount . ' day';
            } elseif ($orderCount >= 8 && $orderCount <= 15) {
                return $orderCount . ' day';
            } elseif ($orderCount >= 16 && $orderCount <= 25) {
                return $orderCount . ' day';
            } elseif ($orderCount >= 26 && $orderCount <= 30) {
                return $orderCount . ' day';
            } else {
                return $orderCount . ' day'; // For any other count
            }

        } catch (\Exception $e) {
            Log::warning('Failed to determine plan type', [
                'order_no' => $orderNo,
                'error' => $e->getMessage()
            ]);

            return 'unknown';
        }
    }

    /**
     * Determine meal type from product name
     *
     * @param string $productName
     * @return string
     */
    protected function determineMealTypeFromProduct(string $productName): string
    {
        $productName = strtolower($productName);

        if (strpos($productName, 'breakfast') !== false) {
            return 'breakfast';
        } elseif (strpos($productName, 'lunch') !== false) {
            return 'lunch';
        } elseif (strpos($productName, 'dinner') !== false) {
            return 'dinner';
        }

        return 'lunch'; // Default to lunch if not specified
    }

    /**
     * Get enhanced product type based on product name and code
     *
     * @param string $productName
     * @param int $productCode
     * @return string
     */
    protected function getEnhancedProductType(string $productName, int $productCode): string
    {
        $productName = strtolower($productName);

        // Product code based mapping (more reliable)
        $productCodeMapping = [
            339 => 'Breakfast',      // Indian Breakfast
            342 => 'Breakfast',      // International Breakfast
            343 => 'Breakfast',      // Breakfast of the Day, Mix Combo Breakfast
            597 => 'Breakfast',      // Jain Breakfast

            336 => 'Lunch',          // Indian Lunch, Happy Meal
            338 => 'Lunch',          // International Lunch
            346 => 'Lunch',          // Lunch of the Day, Mix Combo Lunch
            598 => 'Lunch',          // Jain Lunch
        ];

        // First try product code mapping
        if (isset($productCodeMapping[$productCode])) {
            return $productCodeMapping[$productCode];
        }

        // Fallback to product name analysis
        if (strpos($productName, 'breakfast') !== false) {
            return 'Breakfast';
        } elseif (strpos($productName, 'lunch') !== false) {
            return 'Lunch';
        } elseif (strpos($productName, 'dinner') !== false) {
            return 'Dinner';
        } elseif (strpos($productName, 'meal') !== false) {
            // For generic "meal" products, try to determine from context
            if (strpos($productName, 'happy') !== false) {
                return 'Lunch'; // Happy Meal is typically lunch
            }
            return 'Meal'; // Keep as generic meal
        }

        return 'Meal'; // Default fallback
    }

    /**
     * Extract student name from customer address
     *
     * @param string $customerAddress
     * @return string
     */
    protected function extractStudentName(string $customerAddress): string
    {
        if (empty($customerAddress)) {
            return '';
        }

        // Clean the address
        $address = trim($customerAddress);

        // Try different delimiters in order of preference
        $delimiters = [',', ';', '|', '-'];

        foreach ($delimiters as $delimiter) {
            if (strpos($address, $delimiter) !== false) {
                $parts = explode($delimiter, $address);
                $studentName = trim($parts[0]);

                // Validate the extracted name
                if (!empty($studentName) && strlen($studentName) >= 2 && strlen($studentName) <= 50) {
                    return $studentName;
                }
            }
        }

        // If no delimiter found, try space but only take first word
        $parts = explode(' ', $address);
        $studentName = trim($parts[0]);

        // Validate the extracted name
        if (!empty($studentName) && strlen($studentName) >= 2 && strlen($studentName) <= 50) {
            return $studentName;
        }

        // If still no valid name, return the original address (truncated if too long)
        return strlen($address) > 50 ? substr($address, 0, 50) : $address;
    }

    /**
     * Validate cancellation timing to handle edge cases
     *
     * @param \Illuminate\Support\Collection $ordersToCancel
     * @param \Carbon\Carbon $requestTime
     * @param \Carbon\Carbon $processingTime
     * @return array
     */
    protected function validateCancellationTiming($ordersToCancel, \Carbon\Carbon $requestTime, \Carbon\Carbon $processingTime): array
    {
        try {
            $timeDifference = $processingTime->diffInSeconds($requestTime);
            $maxAllowedDelay = 30; // Maximum 30 seconds delay allowed

            // If processing is significantly delayed, we need to validate more strictly
            if ($timeDifference > $maxAllowedDelay) {
                Log::warning('Significant delay detected in cancellation request processing', [
                    'request_time' => $requestTime->format('Y-m-d H:i:s'),
                    'processing_time' => $processingTime->format('Y-m-d H:i:s'),
                    'delay_seconds' => $timeDifference,
                    'max_allowed_delay' => $maxAllowedDelay
                ]);
            }

            $failedOrders = [];
            $criticalTimeWindow = false;

            foreach ($ordersToCancel as $order) {
                $mealType = $this->determineMealTypeFromProduct($order->product_name);

                // Check policy with request time
                $requestTimePolicy = $this->getCancellationPolicy($order, $mealType, $requestTime);

                // Check policy with processing time
                $processingTimePolicy = $this->getCancellationPolicy($order, $mealType, $processingTime);

                // If policies differ, we have a timing issue
                if ($requestTimePolicy['is_cancellable'] !== $processingTimePolicy['is_cancellable']) {
                    $criticalTimeWindow = true;

                    // If request time allowed cancellation but processing time doesn't, it's an edge case
                    if ($requestTimePolicy['is_cancellable'] && !$processingTimePolicy['is_cancellable']) {
                        $failedOrders[] = [
                            'order_id' => $order->pk_order_no,
                            'order_date' => $order->order_date,
                            'meal_type' => $mealType,
                            'request_time_policy' => $requestTimePolicy['policy_type'],
                            'processing_time_policy' => $processingTimePolicy['policy_type'],
                            'request_time_cancellable' => $requestTimePolicy['is_cancellable'],
                            'processing_time_cancellable' => $processingTimePolicy['is_cancellable'],
                            'request_time' => $requestTime->format('H:i:s'),
                            'processing_time' => $processingTime->format('H:i:s')
                        ];
                    }
                }
            }

            // If we have failed orders due to timing, return validation error
            if (!empty($failedOrders)) {
                $errorMessage = "Cancellation request failed due to timing restrictions. ";
                $errorMessage .= "Your request was submitted at a valid time, but processing was delayed beyond the cancellation window. ";
                $errorMessage .= "Please try again or contact support.";

                return [
                    'is_valid' => false,
                    'error_message' => $errorMessage,
                    'details' => [
                        'request_time' => $requestTime->format('Y-m-d H:i:s'),
                        'processing_time' => $processingTime->format('Y-m-d H:i:s'),
                        'delay_seconds' => $timeDifference,
                        'failed_orders' => $failedOrders,
                        'critical_time_window' => $criticalTimeWindow
                    ]
                ];
            }

            return [
                'is_valid' => true,
                'details' => [
                    'request_time' => $requestTime->format('Y-m-d H:i:s'),
                    'processing_time' => $processingTime->format('Y-m-d H:i:s'),
                    'delay_seconds' => $timeDifference,
                    'critical_time_window' => $criticalTimeWindow
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Failed to validate cancellation timing', [
                'error' => $e->getMessage(),
                'request_time' => $requestTime->format('Y-m-d H:i:s'),
                'processing_time' => $processingTime->format('Y-m-d H:i:s')
            ]);

            // On validation error, allow the cancellation to proceed with processing time
            return [
                'is_valid' => true,
                'details' => [
                    'validation_error' => $e->getMessage(),
                    'fallback_to_processing_time' => true
                ]
            ];
        }
    }

    /**
     * Get cancellation policy based on order and meal type
     *
     * @param object $order
     * @param string $mealType
     * @param \Carbon\Carbon|null $requestTime
     * @return array
     */
    protected function getCancellationPolicy($order, string $mealType, \Carbon\Carbon $requestTime = null): array
    {
        try {
            // Use request time for validation (critical for edge cases like 07:59:59 vs 08:00:01)
            $validationTime = $requestTime ?? now();
            $orderDate = \Carbon\Carbon::parse($order->order_date);
            $validationTimeStr = $validationTime->format('H:i:s');

            Log::info('Cancellation policy validation', [
                'order_id' => $order->pk_order_no ?? 'unknown',
                'meal_type' => $mealType,
                'validation_time' => $validationTime->format('Y-m-d H:i:s'),
                'order_date' => $orderDate->format('Y-m-d'),
                'using_request_timestamp' => $requestTime !== null
            ]);

            // Get settings from database
            $settings = $this->getCancellationSettings($mealType);

            // Determine which day we're checking (0 = same day, 1 = one day before, etc.)
            $cutoffDay = (int) $settings['cutoff_day'];
            $targetDate = $orderDate->copy()->subDays($cutoffDay);

            // Check if we're on the target date for cancellation
            $isTargetDate = $validationTime->toDateString() === $targetDate->toDateString();

            if (!$isTargetDate) {
                // If we're before the target date, full refund allowed
                if ($validationTime->toDateString() < $targetDate->toDateString()) {
                    return [
                        'is_cancellable' => true,
                        'refund_percentage' => 100,
                        'policy_type' => 'full_refund_before_cutoff',
                        'cutoff_time' => $settings['cutoff_time'],
                        'cutoff_day' => $cutoffDay
                    ];
                }

                // If we're after the target date, no cancellation allowed
                return [
                    'is_cancellable' => false,
                    'refund_percentage' => 0,
                    'policy_type' => 'no_cancellation_after_cutoff',
                    'cutoff_time' => $settings['cutoff_time'],
                    'cutoff_day' => $cutoffDay
                ];
            }

            // We're on the target date - check time-based policies
            $cutoffTime = $settings['cutoff_time'];

            // Convert times to comparable format
            $validationTimeMinutes = $this->timeToMinutes($validationTimeStr);
            $cutoffTimeMinutes = $this->timeToMinutes($cutoffTime);
            $partialRefundStartMinutes = $this->timeToMinutes('00:01:00');
            $partialRefundEndMinutes = $this->timeToMinutes('08:00:00');

            // Before cutoff time - full refund
            if ($validationTimeMinutes < $cutoffTimeMinutes) {
                return [
                    'is_cancellable' => true,
                    'refund_percentage' => 100,
                    'policy_type' => 'full_refund_before_cutoff',
                    'cutoff_time' => $cutoffTime,
                    'cutoff_day' => $cutoffDay
                ];
            }

            // Between 00:01:00 to 08:00:00 - partial refund
            if ($validationTimeMinutes >= $partialRefundStartMinutes && $validationTimeMinutes <= $partialRefundEndMinutes) {
                $refundPercentage = $this->getPartialRefundPercentage($mealType, $settings);

                return [
                    'is_cancellable' => $refundPercentage > 0,
                    'refund_percentage' => $refundPercentage,
                    'policy_type' => 'partial_refund_window',
                    'cutoff_time' => $cutoffTime,
                    'cutoff_day' => $cutoffDay
                ];
            }

            // After 08:00:00 - no cancellation
            return [
                'is_cancellable' => false,
                'refund_percentage' => 0,
                'policy_type' => 'no_cancellation_after_8am',
                'cutoff_time' => $cutoffTime,
                'cutoff_day' => $cutoffDay
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get cancellation policy', [
                'order_id' => $order->pk_order_no ?? 'unknown',
                'meal_type' => $mealType,
                'error' => $e->getMessage()
            ]);

            // Default to no cancellation on error
            return [
                'is_cancellable' => false,
                'refund_percentage' => 0,
                'policy_type' => 'error_no_cancellation',
                'cutoff_time' => '00:00:00',
                'cutoff_day' => 0
            ];
        }
    }

    /**
     * Get cancellation settings from database
     *
     * @param string $mealType
     * @return array
     */
    protected function getCancellationSettings(string $mealType): array
    {
        try {
            $mealTypeUpper = strtoupper($mealType);
            $kitchenId = 1; // Default kitchen ID

            // Get settings from database
            $settings = DB::table('settings')
                ->whereIn('key', [
                    "K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_TIME",
                    "K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_DAY",
                    "K{$kitchenId}_{$mealTypeUpper}_ORDER_CANCEL_CUT_OFF_TIME",
                    "K{$kitchenId}_{$mealTypeUpper}_ORDER_CANCEL_CUT_OFF_DAY",
                    "{$mealTypeUpper}_PARTIAL_REFUND_PERCENTAGE",
                    "BREAKFAST_PARTIAL_REFUND_PERCENTAGE",
                    "LUNCH_PARTIAL_REFUND_PERCENTAGE"
                ])
                ->pluck('value', 'key')
                ->toArray();

            // Set defaults if not found
            $cutoffTime = $settings["K{$kitchenId}_{$mealTypeUpper}_ORDER_CANCEL_CUT_OFF_TIME"]
                         ?? $settings["K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_TIME"]
                         ?? '00:01:00';

            $cutoffDay = $settings["K{$kitchenId}_{$mealTypeUpper}_ORDER_CANCEL_CUT_OFF_DAY"]
                        ?? $settings["K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_DAY"]
                        ?? '0';

            return [
                'cutoff_time' => $cutoffTime,
                'cutoff_day' => $cutoffDay,
                'breakfast_partial_refund' => $settings['BREAKFAST_PARTIAL_REFUND_PERCENTAGE'] ?? '0',
                'lunch_partial_refund' => $settings['LUNCH_PARTIAL_REFUND_PERCENTAGE'] ?? '50',
                'settings' => $settings
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get cancellation settings', [
                'meal_type' => $mealType,
                'error' => $e->getMessage()
            ]);

            // Return safe defaults
            return [
                'cutoff_time' => '00:01:00',
                'cutoff_day' => '0',
                'breakfast_partial_refund' => '0',
                'lunch_partial_refund' => '50',
                'settings' => []
            ];
        }
    }

    /**
     * Get partial refund percentage based on meal type
     *
     * @param string $mealType
     * @param array $settings
     * @return int
     */
    protected function getPartialRefundPercentage(string $mealType, array $settings): int
    {
        if ($mealType === 'breakfast') {
            return (int) $settings['breakfast_partial_refund']; // 0% for breakfast
        } elseif ($mealType === 'lunch') {
            return (int) $settings['lunch_partial_refund']; // 50% for lunch
        }

        return 0; // Default to 0% for unknown meal types
    }

    /**
     * Convert time string to minutes for comparison
     *
     * @param string $time
     * @return int
     */
    protected function timeToMinutes(string $time): int
    {
        $parts = explode(':', $time);
        return ((int) $parts[0] * 60) + (int) $parts[1];
    }

    /**
     * Unlock wallet amount for cancelled order
     *
     * @param int $customerCode
     * @param string $orderNo
     * @param string $orderDate
     * @param string $mealType
     * @return float
     */
    protected function unlockWalletAmount(int $customerCode, string $orderNo, string $orderDate, string $mealType): float
    {
        try {
            // Find locked wallet entries for this order
            $lockedEntries = DB::table('customer_wallet')
                ->where('fk_customer_code', $customerCode)
                ->where('amount_type', 'lock')
                ->where('reference_no', 'LIKE', '%' . $orderNo . '%')
                ->orWhere('description', 'LIKE', '%' . $orderNo . '%')
                ->get();

            $totalUnlocked = 0;

            foreach ($lockedEntries as $entry) {
                // Update the locked entry to debit (unlock)
                DB::table('customer_wallet')
                    ->where('customer_wallet_id', $entry->customer_wallet_id)
                    ->update([
                        'amount_type' => 'dr',
                        'payment_type' => 'wallet',
                        'description' => $entry->description . ' | UNLOCKED due to cancellation',
                        'updated_at' => now()
                    ]);

                $totalUnlocked += $entry->wallet_amount;

                Log::info('Wallet amount unlocked', [
                    'customer_code' => $customerCode,
                    'wallet_id' => $entry->customer_wallet_id,
                    'amount' => $entry->wallet_amount,
                    'order_no' => $orderNo,
                    'meal_type' => $mealType
                ]);
            }

            return $totalUnlocked;

        } catch (\Exception $e) {
            Log::error('Failed to unlock wallet amount', [
                'customer_code' => $customerCode,
                'order_no' => $orderNo,
                'order_date' => $orderDate,
                'meal_type' => $mealType,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * Add isCancellable flag to order data
     *
     * @param array $order
     * @return array
     */
    protected function addCancellableFlag(array $order): array
    {
        try {
            // Determine meal type
            $mealType = $this->determineMealTypeFromProduct($order['product_name'] ?? '');

            // Create a mock order object for policy check
            $mockOrder = (object) [
                'pk_order_no' => $order['order_id'] ?? 0,
                'order_date' => $order['delivery_date'] ?? $order['order_date'] ?? now()->toDateString(),
                'product_name' => $order['product_name'] ?? ''
            ];

            // Get cancellation policy
            $policy = $this->getCancellationPolicy($mockOrder, $mealType);

            $order['is_cancellable'] = $policy['is_cancellable'];
            $order['cancellation_policy'] = [
                'refund_percentage' => $policy['refund_percentage'],
                'policy_type' => $policy['policy_type'],
                'cutoff_time' => $policy['cutoff_time'],
                'cutoff_day' => $policy['cutoff_day']
            ];

        } catch (\Exception $e) {
            Log::error('Failed to add cancellable flag', [
                'order' => $order,
                'error' => $e->getMessage()
            ]);

            // Default to not cancellable on error
            $order['is_cancellable'] = false;
            $order['cancellation_policy'] = [
                'refund_percentage' => 0,
                'policy_type' => 'error',
                'cutoff_time' => '00:00:00',
                'cutoff_day' => 0
            ];
        }

        return $order;
    }

    /**
     * Lock wallet amount for order (for cancellation tracking)
     *
     * @param int $customerCode
     * @param string $orderNo
     * @param string $orderDate
     * @param float $amount
     * @param string $mealType
     * @return void
     */
    protected function lockWalletAmountForOrder(int $customerCode, string $orderNo, string $orderDate, float $amount, string $mealType): void
    {
        try {
            $order = DB::table('orders')->where('order_no', $orderNo)->first(['company_id', 'unit_id']);
            $companyId = (int)($order->company_id ?? 0);
            $unitId = (int)($order->unit_id ?? 0);
            // Create wallet lock entry for this order
            $walletId = DB::table('customer_wallet')->insertGetId([
                'company_id' => $companyId,
                'unit_id' => $unitId,
                'fk_customer_code' => $customerCode,
                'wallet_amount' => $amount,
                'amount_type' => 'lock', // Lock the amount
                'reference_no' => 'LOCK_' . $orderNo . '_' . $orderDate . '_' . time(),
                'payment_date' => now()->toDateString(),
                'description' => "Locked Rs. {$amount} for {$mealType} order {$orderNo} on {$orderDate}",
                'created_by' => (int) (request()->input('user_id') ?? 0),
                'updated_by' => (int) (request()->input('user_id') ?? 0),
                'context' => 'customer',
                'payment_type' => 'lock',
                'created_at' => now(),
                'updated_at' => now()
            ]);

            Log::info('Wallet amount locked for order', [
                'customer_code' => $customerCode,
                'wallet_id' => $walletId,
                'amount' => $amount,
                'order_no' => $orderNo,
                'order_date' => $orderDate,
                'meal_type' => $mealType
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to lock wallet amount for order', [
                'customer_code' => $customerCode,
                'order_no' => $orderNo,
                'order_date' => $orderDate,
                'amount' => $amount,
                'meal_type' => $mealType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Swap order product with another product from the same category
     * Updates both orders and order_details tables
     *
     * @param Request $request
     * @param string $orderNo
     * @return JsonResponse
     */
    public function swapOrder(Request $request, string $orderNo): JsonResponse
    {
        try {
            // Validate request
            $validated = $request->validate([
                'order_date' => 'required|date',
                'new_product_code' => 'required|integer',
                'reason' => 'string|max:255',
                'meal_type' => 'string|in:breakfast,lunch,dinner'
            ]);

            DB::beginTransaction();

            Log::info('Order swap request received', [
                'order_no' => $orderNo,
                'order_date' => $validated['order_date'],
                'new_product_code' => $validated['new_product_code'],
                'reason' => $validated['reason'] ?? 'Customer requested swap',
                'meal_type' => $validated['meal_type'] ?? null
            ]);

            // Find the order to swap
            $orderQuery = DB::table('orders')
                ->where('order_no', $orderNo)
                ->where('order_date', $validated['order_date']);

            // Add meal type filter if provided
            if (isset($validated['meal_type'])) {
                $orderQuery->where('order_menu', strtolower($validated['meal_type']));
            }

            $order = $orderQuery->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found for the specified date' . (isset($validated['meal_type']) ? ' and meal type' : ''),
                    'data' => [
                        'order_no' => $orderNo,
                        'order_date' => $validated['order_date'],
                        'meal_type' => $validated['meal_type'] ?? null
                    ]
                ], 404);
            }

            // Check if order can be swapped (not cancelled, delivered, etc.)
            if (!$this->isOrderSwappable($order)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order cannot be swapped due to its current status',
                    'data' => [
                        'order_id' => $order->pk_order_no,
                        'order_status' => $order->order_status,
                        'delivery_status' => $order->delivery_status
                    ]
                ], 400);
            }

            // Get current product details
            $currentProduct = DB::table('products')
                ->where('pk_product_code', $order->product_code)
                ->first();

            if (!$currentProduct) {
                return response()->json([
                    'success' => false,
                    'message' => 'Current product not found',
                    'data' => ['product_code' => $order->product_code]
                ], 404);
            }

            // Get new product details
            $newProduct = DB::table('products')
                ->where('pk_product_code', $validated['new_product_code'])
                ->first();

            if (!$newProduct) {
                return response()->json([
                    'success' => false,
                    'message' => 'New product not found',
                    'data' => ['new_product_code' => $validated['new_product_code']]
                ], 404);
            }

            // Validate products are in the same category
            if (!$this->areProductsSwappable($currentProduct, $newProduct)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Products are not in the same category or not swappable',
                    'data' => [
                        'current_product' => [
                            'code' => $currentProduct->pk_product_code,
                            'name' => $currentProduct->name,
                            'category' => $currentProduct->product_category ?? $currentProduct->category,
                            'type' => $currentProduct->product_type
                        ],
                        'new_product' => [
                            'code' => $newProduct->pk_product_code,
                            'name' => $newProduct->name,
                            'category' => $newProduct->product_category ?? $newProduct->category,
                            'type' => $newProduct->product_type
                        ]
                    ]
                ], 400);
            }

            // Calculate price difference and swap charges
            $priceDifference = (float) $newProduct->unit_price - (float) $currentProduct->unit_price;
            $swapCharges = (float) ($newProduct->swap_charges ?? 0);
            $totalAmountChange = $priceDifference + $swapCharges;

            // Update the order
            $updatedOrder = $this->updateOrderForSwap($order, $newProduct, $totalAmountChange, $validated['reason'] ?? 'Customer requested swap');

            // Update order details
            $updatedOrderDetails = $this->updateOrderDetailsForSwap($order, $newProduct, $totalAmountChange);

            // Log the swap
            $this->logOrderSwap($order, $currentProduct, $newProduct, $priceDifference, $swapCharges, $validated['reason'] ?? 'Customer requested swap');

            DB::commit();

            Log::info('Order swap completed successfully', [
                'order_id' => $order->pk_order_no,
                'order_no' => $orderNo,
                'order_date' => $validated['order_date'],
                'old_product_code' => $currentProduct->pk_product_code,
                'new_product_code' => $newProduct->pk_product_code,
                'price_difference' => $priceDifference,
                'swap_charges' => $swapCharges,
                'total_amount_change' => $totalAmountChange
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Order swapped successfully',
                'data' => [
                    'order_id' => $order->pk_order_no,
                    'order_no' => $orderNo,
                    'order_date' => $validated['order_date'],
                    'swap_details' => [
                        'old_product' => [
                            'code' => $currentProduct->pk_product_code,
                            'name' => $currentProduct->name,
                            'price' => (float) $currentProduct->unit_price
                        ],
                        'new_product' => [
                            'code' => $newProduct->pk_product_code,
                            'name' => $newProduct->name,
                            'price' => (float) $newProduct->unit_price
                        ],
                        'price_difference' => $priceDifference,
                        'swap_charges' => $swapCharges,
                        'total_amount_change' => $totalAmountChange,
                        'new_order_amount' => $updatedOrder['new_amount']
                    ],
                    'reason' => $validated['reason'] ?? 'Customer requested swap'
                ]
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();

            Log::warning('Order swap validation failed', [
                'order_no' => $orderNo,
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Order swap failed', [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Order swap failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if an order can be swapped
     *
     * @param object $order
     * @return bool
     */
    protected function isOrderSwappable(object $order): bool
    {
        // Orders can be swapped if they are not cancelled, delivered, or in processing
        $nonSwappableStatuses = ['Cancelled', 'Delivered', 'Complete'];
        $nonSwappableDeliveryStatuses = ['Delivered', 'Failed', 'Dispatched'];

        if (in_array($order->order_status, $nonSwappableStatuses)) {
            return false;
        }

        if (in_array($order->delivery_status, $nonSwappableDeliveryStatuses)) {
            return false;
        }

        // Check if order date is in the future (can't swap past orders)
        $orderDate = \Carbon\Carbon::parse($order->order_date);
        $today = \Carbon\Carbon::today();

        if ($orderDate->lt($today)) {
            return false;
        }

        return true;
    }

    /**
     * Check if two products can be swapped (same category)
     *
     * @param object $currentProduct
     * @param object $newProduct
     * @return bool
     */
    protected function areProductsSwappable(object $currentProduct, object $newProduct): bool
    {
        // Check if both products are active
        if (!$currentProduct->status || !$newProduct->status) {
            return false;
        }

        // Check if new product is swappable
        if (isset($newProduct->is_swappable) && !$newProduct->is_swappable) {
            return false;
        }

        // Check if products are in the same category
        $currentCategory = $currentProduct->product_category ?? $currentProduct->category ?? null;
        $newCategory = $newProduct->product_category ?? $newProduct->category ?? null;

        if (!$currentCategory || !$newCategory || $currentCategory !== $newCategory) {
            return false;
        }

        // Check if products are of the same type
        if ($currentProduct->product_type !== $newProduct->product_type) {
            return false;
        }

        // Check if products have the same food type (veg/non-veg)
        if (isset($currentProduct->food_type) && isset($newProduct->food_type)) {
            if ($currentProduct->food_type !== $newProduct->food_type) {
                return false;
            }
        }

        return true;
    }

    /**
     * Update order record for swap
     *
     * @param object $order
     * @param object $newProduct
     * @param float $totalAmountChange
     * @param string $reason
     * @return array
     */
    protected function updateOrderForSwap(object $order, object $newProduct, float $totalAmountChange, string $reason): array
    {
        $newAmount = (float) $order->amount + $totalAmountChange;
        $newTax = $this->calculateOrderTax($newAmount, null);

        $updateData = [
            'product_code' => $newProduct->pk_product_code,
            'product_name' => $newProduct->name,
            'product_description' => $newProduct->description ?? $newProduct->name,
            'amount' => $newAmount,
            'tax' => $newTax,
            'last_modified' => now(),
            'remark' => ($order->remark ? $order->remark . '; ' : '') . "Swapped to {$newProduct->name}. Reason: {$reason}"
        ];

        DB::table('orders')
            ->where('pk_order_no', $order->pk_order_no)
            ->update($updateData);

        Log::info('Order updated for swap', [
            'order_id' => $order->pk_order_no,
            'old_product_code' => $order->product_code,
            'new_product_code' => $newProduct->pk_product_code,
            'old_amount' => $order->amount,
            'new_amount' => $newAmount,
            'amount_change' => $totalAmountChange
        ]);

        return [
            'new_amount' => $newAmount,
            'new_tax' => $newTax,
            'update_data' => $updateData
        ];
    }

    /**
     * Update order details for swap
     *
     * @param object $order
     * @param object $newProduct
     * @param float $totalAmountChange
     * @return array
     */
    protected function updateOrderDetailsForSwap(object $order, object $newProduct, float $totalAmountChange): array
    {
        // Get existing order details
        $orderDetails = DB::table('order_details')
            ->where('ref_order_no', $order->order_no)
            ->where('order_date', $order->order_date)
            ->get();

        $updatedDetails = [];

        foreach ($orderDetails as $detail) {
            // Calculate new amounts proportionally
            $proportionalChange = $totalAmountChange * ((float) $detail->product_amount / (float) $order->amount);
            $newDetailAmount = (float) $detail->product_amount + $proportionalChange;
            $newDetailTax = $this->calculateOrderTax($newDetailAmount, null);

            $updateDetailData = [
                'product_code' => $newProduct->pk_product_code,
                'product_name' => $newProduct->name,
                'product_amount' => $newDetailAmount,
                'product_tax' => $newDetailTax,
                'product_generic_code' => $newProduct->pk_product_code,
                'product_generic_name' => $newProduct->name
            ];

            DB::table('order_details')
                ->where('pk_order_detail_id', $detail->pk_order_detail_id)
                ->update($updateDetailData);

            $updatedDetails[] = [
                'detail_id' => $detail->pk_order_detail_id,
                'old_amount' => $detail->product_amount,
                'new_amount' => $newDetailAmount,
                'amount_change' => $proportionalChange
            ];
        }

        Log::info('Order details updated for swap', [
            'order_no' => $order->order_no,
            'order_date' => $order->order_date,
            'details_updated' => count($updatedDetails),
            'updated_details' => $updatedDetails
        ]);

        return $updatedDetails;
    }

    /**
     * Log order swap for audit trail
     *
     * @param object $order
     * @param object $currentProduct
     * @param object $newProduct
     * @param float $priceDifference
     * @param float $swapCharges
     * @param string $reason
     * @return void
     */
    protected function logOrderSwap(object $order, object $currentProduct, object $newProduct, float $priceDifference, float $swapCharges, string $reason): void
    {
        // Create audit log entry (you can create a separate table for this)
        Log::info('Order swap audit log', [
            'order_id' => $order->pk_order_no,
            'order_no' => $order->order_no,
            'order_date' => $order->order_date,
            'customer_code' => $order->customer_code,
            'customer_name' => $order->customer_name,
            'swap_timestamp' => now()->toISOString(),
            'old_product' => [
                'code' => $currentProduct->pk_product_code,
                'name' => $currentProduct->name,
                'price' => $currentProduct->unit_price,
                'category' => $currentProduct->product_category ?? $currentProduct->category
            ],
            'new_product' => [
                'code' => $newProduct->pk_product_code,
                'name' => $newProduct->name,
                'price' => $newProduct->unit_price,
                'category' => $newProduct->product_category ?? $newProduct->category
            ],
            'financial_impact' => [
                'price_difference' => $priceDifference,
                'swap_charges' => $swapCharges,
                'total_change' => $priceDifference + $swapCharges,
                'old_order_amount' => $order->amount,
                'new_order_amount' => (float) $order->amount + $priceDifference + $swapCharges
            ],
            'reason' => $reason,
            'swap_initiated_by' => 'api', // Could be enhanced to track actual user
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Get cancellation details for an order
     *
     * @param object $order
     * @return array|null
     */
    protected function getCancellationDetails(object $order): ?array
    {
        // Check if order is cancelled
        $orderStatus = strtolower($order->order_status ?? '');
        $deliveryStatus = strtolower($order->delivery_status ?? '');

        $isCancelled = in_array($orderStatus, ['cancelled', 'canceled', 'cancel', 'refunded']) ||
                      in_array($deliveryStatus, ['cancelled', 'canceled', 'cancel']);

        if (!$isCancelled) {
            return null;
        }

        // Parse cancellation details from remark field
        $remark = $order->cancellation_reason ?? '';
        $cancelledBy = $this->extractCancelledBy($remark);
        $cancelledOn = $this->extractCancelledOn($remark);

        // Get refund details from customer_wallet if available
        $refundDetails = $this->getRefundDetails($order->order_no, $order->order_date);

        return [
            'cancelled_by' => $cancelledBy,
            'cancelled_on' => $cancelledOn,
            'cancellation_reason' => $remark,
            'refund_amount' => $refundDetails['refund_amount'] ?? 0.00,
            'refund_percentage' => $refundDetails['refund_percentage'] ?? 0,
            'refund_policy_applied' => $refundDetails['policy_type'] ?? 'unknown'
        ];
    }

    /**
     * Extract cancelled_by from remark field
     *
     * @param string $remark
     * @return string
     */
    protected function extractCancelledBy(string $remark): string
    {
        if (preg_match('/Cancelled by:\s*([^|]+)/', $remark, $matches)) {
            return trim($matches[1]);
        }
        return 'system';
    }

    /**
     * Extract cancelled_on from remark field
     *
     * @param string $remark
     * @return string|null
     */
    protected function extractCancelledOn(string $remark): ?string
    {
        if (preg_match('/Cancelled on:\s*([^|]+)/', $remark, $matches)) {
            return trim($matches[1]);
        }
        return null;
    }

    /**
     * Get refund details from wallet transactions
     *
     * @param string $orderNo
     * @param string $orderDate
     * @return array
     */
    protected function getRefundDetails(string $orderNo, string $orderDate): array
    {
        try {
            // Look for refund transaction in customer_wallet
            $refundTransaction = DB::table('customer_wallet')
                ->where('reference_no', 'LIKE', "%{$orderNo}%")
                ->where('amount_type', 'cr')
                ->where('context', 'order_cancellation')
                ->orderBy('created_at', 'desc')
                ->first(['wallet_amount', 'description']);

            if ($refundTransaction) {
                $refundAmount = (float) $refundTransaction->wallet_amount;

                // Try to extract refund percentage from description
                $description = $refundTransaction->description ?? '';
                $refundPercentage = 0;

                if (preg_match('/(\d+)%/', $description, $matches)) {
                    $refundPercentage = (int) $matches[1];
                }

                // Determine policy type from description
                $policyType = 'unknown';
                if (strpos($description, '100%') !== false) {
                    $policyType = 'full_refund_before_cutoff';
                } elseif (strpos($description, '50%') !== false || strpos($description, 'partial') !== false) {
                    $policyType = 'partial_refund_window';
                } elseif (strpos($description, '0%') !== false || $refundAmount == 0) {
                    $policyType = 'no_refund_after_cutoff';
                }

                return [
                    'refund_amount' => $refundAmount,
                    'refund_percentage' => $refundPercentage,
                    'policy_type' => $policyType
                ];
            }

            return [
                'refund_amount' => 0.00,
                'refund_percentage' => 0,
                'policy_type' => 'no_refund'
            ];

        } catch (Exception $e) {
            // Log::warning('Failed to get refund details', [
            //     'order_no' => $orderNo,
            //     'order_date' => $orderDate,
            //     'error' => $e->getMessage()
            // ]);

            return [
                'refund_amount' => 0.00,
                'refund_percentage' => 0,
                'policy_type' => 'error'
            ];
        }
    }

    /**
     * Get specific order first when order_no is provided for immediate display
     *
     * @param int $customerId
     * @param string $specificOrderNo
     * @param object $customer
     * @param Request $request
     * @return JsonResponse
     */
    protected function getSpecificOrderFirst(int $customerId, string $specificOrderNo, object $customer, Request $request): JsonResponse
    {
        // Get the specific order first
        $specificOrderQuery = DB::table('orders as o')
            ->join('products','o.product_code','products.pk_product_code')
            ->where('o.customer_code', $customerId)
            ->where('o.order_no', $specificOrderNo)
            ->select([
                'o.pk_order_no as order_id',
                'o.order_no',
                'o.company_id',
                'o.order_date',
                'o.order_date as delivery_date',
                'o.order_status',
                'o.delivery_status',
                'o.payment_mode',
                'o.amount_paid',
                'o.amount as total_amount',
                'o.delivery_time',
                'o.delivery_end_time',
                DB::raw('1 as recurring_status'),
                DB::raw('"" as days_preference'),
                'o.ship_address as customer_address',
                'o.location_name',
                'o.city_name',
                'o.food_type as food_preference',
                'o.product_code',
                'o.product_name',
                'products.image_path',
                'o.quantity',
                'o.amount as item_amount',
                'o.product_type',
                'o.last_modified'
            ]);

        $specificOrders = $specificOrderQuery->get();

        if ($specificOrders->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Specific order not found'
            ], 404);
        }

        // Process the specific order
        $groupedSpecificOrders = $this->groupAndCategorizeOrders($specificOrders);
        $studentNames = $this->extractStudentNames($groupedSpecificOrders);

        Log::info('Specific order retrieved successfully', [
            'customer_id' => $customerId,
            'order_no' => $specificOrderNo,
            'orders_found' => count($specificOrders)
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'customer' => [
                    'customer_id' => $customer->pk_customer_code,
                    'name' => $customer->customer_name,
                    'email' => $customer->email_address,
                    'phone' => $customer->phone
                ],
                'summary' => [
                    'total_orders' => count($groupedSpecificOrders['all']),
                    'upcoming_orders' => count($groupedSpecificOrders['upcoming']),
                    'cancelled_orders' => count($groupedSpecificOrders['cancelled']),
                    'other_orders' => count($groupedSpecificOrders['other'])
                ],
                'student_names' => $studentNames,
                'filters' => [
                    'specific_order_no' => $specificOrderNo,
                    'priority_mode' => true
                ],
                'orders' => [
                    'upcoming' => $groupedSpecificOrders['upcoming'],
                    'cancelled' => $groupedSpecificOrders['cancelled'],
                    'other' => $groupedSpecificOrders['other']
                ]
            ]
        ]);
    }

    /**
     * Build optimized order query with filters
     *
     * @param int $customerId
     * @param string|null $studentNameFilter
     * @param bool $includeCancelled
     * @param string|null $orderStatus
     * @param string|null $startDate format Y-m-d
     * @param string|null $endDate format Y-m-d
     * @return \Illuminate\Database\Query\Builder
     */
    protected function buildOptimizedOrderQuery(int $customerId, ?string $studentNameFilter, bool $includeCancelled, ?string $orderStatus, ?string $startDate = null, ?string $endDate = null)
    {
        $query = DB::table('orders as o')
            ->leftJoin('products', 'o.product_code', '=', 'products.pk_product_code')
            ->where('o.customer_code', $customerId);

        // Apply student name filter if provided
        if ($studentNameFilter) {
            $query->where('o.ship_address', 'LIKE', $studentNameFilter . '%');
            Log::info('Applied student name filter', [
                'customer_id' => $customerId,
                'student_name_filter' => $studentNameFilter
            ]);
        }

        // Apply order status filter if provided
        if ($orderStatus) {
            $query->where('o.order_status', 'LIKE', '%' . $orderStatus . '%');
            Log::info('Applied order status filter', [
                'customer_id' => $customerId,
                'order_status' => $orderStatus
            ]);
        }

        // Note: Do not exclude cancelled at query level.
        // We always fetch all orders and categorize them later.
        Log::info('Including cancelled orders in base query; filtering handled in categorization', [
            'customer_id' => $customerId,
            'include_cancelled' => $includeCancelled
        ]);

        // Apply date range filters on order_date if provided
        if ($startDate && $endDate) {
            $query->whereBetween('o.order_date', [$startDate, $endDate]);
            Log::info('Applied date range filter', [
                'customer_id' => $customerId,
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);
        } elseif ($startDate) {
            $query->whereDate('o.order_date', '>=', $startDate);
            Log::info('Applied start date filter', [
                'customer_id' => $customerId,
                'start_date' => $startDate
            ]);
        } elseif ($endDate) {
            $query->whereDate('o.order_date', '<=', $endDate);
            Log::info('Applied end date filter', [
                'customer_id' => $customerId,
                'end_date' => $endDate
            ]);
        }

        return $query->select([
            'o.pk_order_no as order_id',
            'o.order_no',
            'o.company_id',
            'o.order_date',
            'o.order_date as delivery_date',
            'o.order_status',
            'o.delivery_status',
            'o.payment_mode',
            'o.amount_paid',
            'o.amount as total_amount',
            'o.delivery_time',
            'o.delivery_end_time',
            DB::raw('1 as recurring_status'),
            DB::raw('"" as days_preference'),
            'o.ship_address as customer_address',
            'o.location_name',
            'o.city_name',
            'o.food_type as food_preference',
            'o.product_code',
            'o.product_name',
            'products.image_path',
            'o.quantity',
            'o.amount as item_amount',
            'o.product_type',
            'o.last_modified'
            ])->orderBy('o.last_modified', 'desc')
            ->orderBy('o.order_date', 'desc');
    }

    /**
     * Paginate a plain array and return data with meta
     *
     * @param array $items
     * @param int $page
     * @param int $perPage
     * @return array{data: array, meta: array}
     */
    protected function paginateArray(array $items, int $page = 1, int $perPage = 10): array
    {
        $total = count($items);
        $page = max(1, $page);
        $perPage = max(1, $perPage);
        $lastPage = (int) ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;
        $data = array_slice($items, $offset, $perPage);
        $from = $total > 0 ? $offset + 1 : 0;
        $to = $total > 0 ? min($offset + count($data), $total) : 0;

        return [
            'data' => $data,
            'meta' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => $lastPage,
                'from' => $from,
                'to' => $to,
                'has_more' => $page < $lastPage
            ]
        ];
    }

    /**
     * Get customer wallet balance
     *
     * @param int $customerId
     * @return float
     */
    protected function getCustomerWalletBalance(int $customerId): float
    {
        try {
            // Calculate wallet balance from customer_wallet table
            $walletData = DB::table('customer_wallet')
                ->select([
                    DB::raw("SUM(CASE WHEN amount_type = 'cr' THEN wallet_amount ELSE 0 END) as total_credit"),
                    DB::raw("SUM(CASE WHEN amount_type = 'dr' THEN wallet_amount ELSE 0 END) as total_debit"),
                    DB::raw("SUM(CASE WHEN amount_type = 'lock' THEN wallet_amount ELSE 0 END) as total_locked")
                ])
                ->where('fk_customer_code', $customerId)
                ->first();

            $totalCredit = (float) ($walletData->total_credit ?? 0);
            $totalDebit = (float) ($walletData->total_debit ?? 0);
            $totalLocked = (float) ($walletData->total_locked ?? 0);

            // Available balance = Credits - Debits - Locked amounts
            $availableBalance = $totalCredit - $totalDebit - $totalLocked;

            Log::info('Customer wallet balance calculated', [
                'customer_id' => $customerId,
                'total_credit' => $totalCredit,
                'total_debit' => $totalDebit,
                'total_locked' => $totalLocked,
                'available_balance' => $availableBalance
            ]);

            return max(0, $availableBalance); // Ensure non-negative balance

        } catch (Exception $e) {
            Log::error('Failed to get customer wallet balance', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);

            return 0.0; // Return 0 balance on error
        }
    }

    /**
     * Get payment description based on payment method
     *
     * @param string $paymentMethod
     * @param float $walletAmount
     * @param float $gatewayAmount
     * @return string
     */
    protected function getPaymentDescription(string $paymentMethod, float $walletAmount, float $gatewayAmount): string
    {
        switch ($paymentMethod) {
            case 'wallet':
                return "Payment completed via wallet (₹{$walletAmount})";
            case 'mixed':
                return "Mixed payment: ₹{$walletAmount} from wallet + ₹{$gatewayAmount} via gateway";
            case 'online':
            default:
                return "Payment initiated for meal subscription (₹{$gatewayAmount})";
        }
    }

    /**
     * Process wallet deduction for wallet/mixed payments
     *
     * @param int $customerId
     * @param float $walletAmount
     * @param string $orderNo
     * @param int $companyId
     * @param int $unitId
     * @return bool
     */
    protected function processWalletDeduction(int $customerId, float $walletAmount, string $orderNo, int $companyId, int $unitId): bool
    {
        try {
            if ($walletAmount <= 0) {
                return true; // No wallet deduction needed
            }

            // Create wallet debit entry
            DB::table('customer_wallet')->insert([
                'company_id' => $companyId,
                'unit_id' => $unitId,
                'fk_customer_code' => $customerId,
                'wallet_amount' => $walletAmount,
                'amount_type' => 'dr', // Debit
                'reference_no' => $orderNo,
                'payment_type' => 'wallet',
                'payment_date' => now()->toDateString(),
                'description' => "₹{$walletAmount} deducted for order {$orderNo}",
                'created_by' => $customerId,
                'created_date' => now(),
                'updated_by' => $customerId,
                'updated_date' => now(),
                'context' => 'customer',
            ]);

            Log::info('Wallet amount deducted for order', [
                'customer_id' => $customerId,
                'wallet_amount' => $walletAmount,
                'order_no' => $orderNo
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to process wallet deduction', [
                'customer_id' => $customerId,
                'wallet_amount' => $walletAmount,
                'order_no' => $orderNo,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Create wallet debit entries for successful payment
     * This creates the debit entries in customer_wallet table similar to the examples provided
     *
     * @param object $primaryTempPreOrder
     * @param \Illuminate\Support\Collection $allRelatedTempPreOrders
     * @param Request $request
     * @return void
     */
    protected function createWalletDebitEntries(object $primaryTempPreOrder, $allRelatedTempPreOrders, Request $request): void
    {
        try {
            $customerId = $primaryTempPreOrder->customer_code;
            $companyId = $primaryTempPreOrder->company_id;
            $unitId = $primaryTempPreOrder->unit_id;
            $paymentTransactionId = $request->input('payment_service_transaction_id');
            $gateway = $request->input('gateway', 'online');

            // Create wallet credit entry for the payment received
            $totalPaidAmount = 0;
            foreach ($allRelatedTempPreOrders as $tempOrder) {
                $totalPaidAmount += $tempOrder->total_amt;
            }

            // 1. Create credit entry for payment received
            DB::table('customer_wallet')->insert([
                'company_id' => $companyId,
                'unit_id' => $unitId,
                'fk_customer_code' => $customerId,
                'wallet_amount' => $totalPaidAmount,
                'amount_type' => 'cr', // Credit
                'reference_no' => $paymentTransactionId,
                'payment_type' => $gateway,
                'payment_date' => now()->toDateString(),
                'description' => "₹{$totalPaidAmount} received by online payment ( {$paymentTransactionId} )",
                'created_by' => $customerId,
                'created_date' => now(),
                'updated_by' => $customerId,
                'updated_date' => now(),
                'context' => 'customer',
            ]);

            Log::info('Created wallet credit entry for payment', [
                'customer_id' => $customerId,
                'amount' => $totalPaidAmount,
                'transaction_id' => $paymentTransactionId,
                'gateway' => $gateway
            ]);

            // 2. Create debit entries for each order (meal amount + delivery charges separately)
            foreach ($allRelatedTempPreOrders as $tempOrder) {
                $orderAmount = $tempOrder->amount; // Meal amount without delivery charges
                $deliveryCharges = $tempOrder->delivery_charges;

                // Get the first created order for this temp order to get bill number
                $createdOrder = DB::table('orders')
                    ->where('ref_order', $tempOrder->pk_order_no)
                    ->first(['pk_order_no']);

                $billNo = $createdOrder ? $createdOrder->pk_order_no : 'N/A';

                // Debit entry for meal amount
                if ($orderAmount > 0) {
                    DB::table('customer_wallet')->insert([
                        'company_id' => $companyId,
                        'unit_id' => $unitId,
                        'fk_customer_code' => $customerId,
                        'wallet_amount' => $orderAmount,
                        'amount_type' => 'dr', // Debit
                        'reference_no' => null,
                        'payment_type' => null,
                        'payment_date' => now()->toDateString(),
                        'description' => "Rs. {$orderAmount} deducted against Bill No. {$billNo} of Order No. {$tempOrder->order_no}",
                        'created_by' => $customerId,
                        'created_date' => now(),
                        'updated_by' => $customerId,
                        'updated_date' => now(),
                        'context' => 'admin',
                        'payment_type' => 'wallet'
                    ]);
                }

                // Debit entry for delivery charges (if any)
                if ($deliveryCharges > 0) {
                    DB::table('customer_wallet')->insert([
                        'company_id' => $companyId,
                        'unit_id' => $unitId,
                        'fk_customer_code' => $customerId,
                        'wallet_amount' => $deliveryCharges,
                        'amount_type' => 'dr', // Debit
                        'reference_no' => null,
                        'payment_type' => null,
                        'payment_date' => now()->toDateString(),
                        'description' => "Rs. {$deliveryCharges} delivery charges deducted against Bill No. {$billNo} of Order No. {$tempOrder->order_no}",
                        'created_by' => $customerId,
                        'created_date' => now(),
                        'updated_by' => $customerId,
                        'updated_date' => now(),
                        'context' => 'admin',
                        'payment_type' => 'wallet'
                    ]);
                }

                Log::info('Created wallet debit entries for order', [
                    'customer_id' => $customerId,
                    'order_no' => $tempOrder->order_no,
                    'bill_no' => $billNo,
                    'meal_amount' => $orderAmount,
                    'delivery_charges' => $deliveryCharges
                ]);
            }

        } catch (Exception $e) {
            Log::error('Failed to create wallet debit entries', [
                'customer_id' => $primaryTempPreOrder->customer_code ?? 'unknown',
                'primary_order_no' => $primaryTempPreOrder->order_no ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Don't throw the exception to avoid breaking the payment success flow
            // The wallet entries are important but not critical enough to fail the entire payment
        }
    }

    /**
     * Create final orders for wallet payment
     *
     * @param array $tempPreOrderIds
     * @param array $validated
     * @param int $paymentTransactionId
     * @return void
     */
    protected function createFinalOrdersForWalletPayment(array $tempPreOrderIds, array $validated, int $paymentTransactionId): void
    {
        try {
            Log::info('Creating final orders for wallet payment', [
                'temp_pre_order_ids' => array_column($tempPreOrderIds, 'id'),
                'payment_transaction_id' => $paymentTransactionId,
                'customer_id' => $validated['customer_id']
            ]);

            $allCreatedOrders = [];

            // Create actual orders from each temp_pre_order
            foreach ($tempPreOrderIds as $tempPreOrderData) {
                // Get the temp_pre_order record
                $tempPreOrder = DB::table('temp_pre_orders')
                    ->where('pk_order_no', $tempPreOrderData['id'])
                    ->first();

                if (!$tempPreOrder) {
                    Log::warning('Temp pre-order not found', [
                        'temp_pre_order_id' => $tempPreOrderData['id']
                    ]);
                    continue;
                }

                // Create a mock request object for compatibility with existing method
                $mockRequest = new \Illuminate\Http\Request();
                $mockRequest->merge([
                    'payment_service_transaction_id' => 'WALLET_' . $paymentTransactionId,
                    'gateway' => 'wallet',
                    'status' => 'completed'
                ]);

                // Create actual orders using existing method
                $createdOrders = $this->createActualOrdersFromTemp($tempPreOrder, $mockRequest);
                $allCreatedOrders = array_merge($allCreatedOrders, $createdOrders);

                Log::info('Created orders from temp pre-order for wallet payment', [
                    'temp_order_no' => $tempPreOrder->order_no,
                    'meal_type' => $tempPreOrder->order_menu,
                    'orders_created' => count($createdOrders)
                ]);
            }

            // Update temp_order_payment status to success
            foreach ($tempPreOrderIds as $tempPreOrderData) {
                $this->updateTempOrderPaymentSuccess($tempPreOrderData['id']);
            }

            Log::info('Final orders created successfully for wallet payment', [
                'total_orders_created' => count($allCreatedOrders),
                'payment_transaction_id' => $paymentTransactionId
            ]);

        } catch (Exception $e) {
            Log::error('Failed to create final orders for wallet payment', [
                'temp_pre_order_ids' => array_column($tempPreOrderIds, 'id'),
                'payment_transaction_id' => $paymentTransactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Don't throw the exception to avoid breaking the payment flow
            // The orders creation failure should be handled gracefully
        }
    }

}
