<?php

namespace App\Traits;

use App\Utils\JwtDecoder;
use Illuminate\Http\Request;

/**
 * JWT Authentication Helper Trait for Customer Service v12
 * 
 * This trait provides convenient methods for controllers to access
 * JWT authentication data and user information.
 */
trait JwtAuthHelper
{
    /**
     * Get authenticated user information
     *
     * @param Request $request
     * @return array|null
     */
    protected function getAuthUser(Request $request): ?array
    {
        return $request->attributes->get('auth_user');
    }

    /**
     * Get current user ID
     *
     * @param Request $request
     * @return string|null
     */
    protected function getCurrentUserId(Request $request): ?string
    {
        $user = $this->getAuthUser($request);
        return $user['user_id'] ?? null;
    }

    /**
     * Get current username
     *
     * @param Request $request
     * @return string|null
     */
    protected function getCurrentUsername(Request $request): ?string
    {
        $user = $this->getAuthUser($request);
        return $user['username'] ?? null;
    }

    /**
     * Get current user email
     *
     * @param Request $request
     * @return string|null
     */
    protected function getCurrentUserEmail(Request $request): ?string
    {
        $user = $this->getAuthUser($request);
        return $user['email'] ?? null;
    }

    /**
     * Get Old SSO User ID
     *
     * @param Request $request
     * @return string|null
     */
    protected function getOldSsoUserId(Request $request): ?string
    {
        $user = $this->getAuthUser($request);
        return $user['old_sso_user_id'] ?? null;
    }

    /**
     * Get all user roles
     *
     * @param Request $request
     * @return array
     */
    protected function getUserRoles(Request $request): array
    {
        $payload = $request->attributes->get('jwt_payload');
        return $payload ? JwtDecoder::extractRoles($payload) : [];
    }

    /**
     * Check if user has specific role
     *
     * @param Request $request
     * @param string $role
     * @param string|null $resource
     * @return bool
     */
    protected function hasRole(Request $request, string $role, ?string $resource = null): bool
    {
        $payload = $request->attributes->get('jwt_payload');
        return $payload ? JwtDecoder::hasRole($payload, $role, $resource) : false;
    }

    /**
     * Check if user is admin
     *
     * @param Request $request
     * @return bool
     */
    protected function isAdmin(Request $request): bool
    {
        return $this->hasRole($request, 'admin');
    }

    /**
     * Get all roles as flat array
     *
     * @param Request $request
     * @return array
     */
    protected function getAllRoles(Request $request): array
    {
        return $this->getUserRoles($request);
    }

    /**
     * Get realm roles only
     *
     * @param Request $request
     * @return array
     */
    protected function getRealmRoles(Request $request): array
    {
        $payload = $request->attributes->get('jwt_payload');
        if (!$payload || !isset($payload['realm_access']['roles'])) {
            return [];
        }
        return $payload['realm_access']['roles'];
    }

    /**
     * Get resource/client roles
     *
     * @param Request $request
     * @param string|null $resource
     * @return array
     */
    protected function getResourceRoles(Request $request, ?string $resource = null): array
    {
        $payload = $request->attributes->get('jwt_payload');
        if (!$payload || !isset($payload['resource_access'])) {
            return [];
        }

        if ($resource) {
            return $payload['resource_access'][$resource]['roles'] ?? [];
        }

        // Return all resource roles
        $roles = [];
        foreach ($payload['resource_access'] as $resourceRoles) {
            if (isset($resourceRoles['roles'])) {
                $roles = array_merge($roles, $resourceRoles['roles']);
            }
        }
        return array_unique($roles);
    }

    /**
     * Get token expiration information
     *
     * @param Request $request
     * @return array|null
     */
    protected function getTokenExpiration(Request $request): ?array
    {
        $payload = $request->attributes->get('jwt_payload');
        return $payload ? JwtDecoder::getExpirationInfo($payload) : null;
    }

    /**
     * Check if token is expired
     *
     * @param Request $request
     * @return bool
     */
    protected function isTokenExpired(Request $request): bool
    {
        $expiration = $this->getTokenExpiration($request);
        return $expiration ? ($expiration['is_expired'] ?? false) : true;
    }

    /**
     * Get token expires in minutes
     *
     * @param Request $request
     * @return int|null
     */
    protected function getTokenExpiresInMinutes(Request $request): ?int
    {
        $expiration = $this->getTokenExpiration($request);
        return $expiration ? $expiration['expires_in_minutes'] : null;
    }

    /**
     * Get client ID from token
     *
     * @param Request $request
     * @return string|null
     */
    protected function getClientId(Request $request): ?string
    {
        $user = $this->getAuthUser($request);
        return $user['client_id'] ?? null;
    }

    /**
     * Get realm from token
     *
     * @param Request $request
     * @return string|null
     */
    protected function getRealm(Request $request): ?string
    {
        $user = $this->getAuthUser($request);
        return $user['realm'] ?? null;
    }

    /**
     * Get session ID from token
     *
     * @param Request $request
     * @return string|null
     */
    protected function getSessionId(Request $request): ?string
    {
        $user = $this->getAuthUser($request);
        return $user['session_id'] ?? null;
    }

    /**
     * Get complete user profile
     *
     * @param Request $request
     * @return array
     */
    protected function getUserProfile(Request $request): array
    {
        $user = $this->getAuthUser($request);
        $expiration = $this->getTokenExpiration($request);
        $roles = $this->getUserRoles($request);

        return [
            'user_info' => $user,
            'roles' => $roles,
            'token_expiration' => $expiration,
            'permissions' => [
                'is_admin' => $this->isAdmin($request),
                'realm_roles' => $this->getRealmRoles($request),
                'resource_roles' => $this->getResourceRoles($request),
            ]
        ];
    }

    /**
     * Create audit context for logging
     *
     * @param Request $request
     * @param string $action
     * @param array $additionalData
     * @return array
     */
    protected function createAuditContext(Request $request, string $action, array $additionalData = []): array
    {
        $user = $this->getAuthUser($request);
        
        return array_merge([
            'action' => $action,
            'user_id' => $user['user_id'] ?? null,
            'username' => $user['username'] ?? null,
            'old_sso_user_id' => $user['old_sso_user_id'] ?? null,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'timestamp' => now()->toIso8601String(),
        ], $additionalData);
    }
}
