const fs = require('fs');

try {
    // Simple YAML validation by checking basic structure
    const content = fs.readFileSync('openapi-customer-service.yaml', 'utf8');
    
    // Check for basic OpenAPI structure
    if (!content.includes('openapi: 3.0.3')) {
        throw new Error('Missing OpenAPI version');
    }
    
    if (!content.includes('info:')) {
        throw new Error('Missing info section');
    }
    
    if (!content.includes('paths:')) {
        throw new Error('Missing paths section');
    }
    
    if (!content.includes('components:')) {
        throw new Error('Missing components section');
    }
    
    // Check for balanced quotes and brackets
    const singleQuotes = (content.match(/'/g) || []).length;
    const doubleQuotes = (content.match(/"/g) || []).length;
    
    console.log('✅ Basic YAML structure validation passed');
    console.log(`📊 File stats: ${content.split('\n').length} lines, ${content.length} characters`);
    console.log(`📝 Quotes: ${singleQuotes} single, ${doubleQuotes} double`);
    
    // Check for common YAML issues
    const lines = content.split('\n');
    let issues = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const lineNum = i + 1;
        
        // Check for tabs (should use spaces)
        if (line.includes('\t')) {
            issues.push(`Line ${lineNum}: Contains tabs (use spaces)`);
        }
        
        // Check for trailing spaces
        if (line.endsWith(' ') && line.trim() !== '') {
            issues.push(`Line ${lineNum}: Trailing spaces`);
        }
    }
    
    if (issues.length > 0) {
        console.log('⚠️  Found potential issues:');
        issues.slice(0, 10).forEach(issue => console.log(`   ${issue}`));
        if (issues.length > 10) {
            console.log(`   ... and ${issues.length - 10} more issues`);
        }
    } else {
        console.log('✅ No obvious YAML syntax issues found');
    }
    
    console.log('🎉 YAML file appears to be valid for import!');
    
} catch (error) {
    console.error('❌ YAML validation failed:', error.message);
    process.exit(1);
}
